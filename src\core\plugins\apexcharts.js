import VueApexCharts from 'vue3-apexcharts';
import app from '../../config/configApp';
import { Calendar } from 'v-calendar';
import ApexCharts from 'apexcharts';

// 設置 ApexCharts 全局默認配置
ApexCharts.setGlobalOptions({
  chart: {
    fontFamily: 'Noto Sans TC, Jost, sans-serif',
    foreColor: '#000000'
  },
  xaxis: {
    labels: {
      style: {
        colors: '#000000'
      }
    },
    title: {
      style: {
        color: '#000000'
      }
    }
  },
  yaxis: {
    labels: {
      style: {
        colors: '#000000'
      }
    },
    title: {
      style: {
        color: '#000000'
      }
    }
  },
  legend: {
    labels: {
      colors: '#000000'
    }
  },
  title: {
    style: {
      color: '#000000'
    }
  },
  subtitle: {
    style: {
      color: '#000000'
    }
  },
  dataLabels: {
    style: {
      colors: ['#000000']
    }
  }
});

// 檢查插件是否已經註冊，避免重複註冊
if (!app.config.globalProperties.$apexcharts) {
  app.config.globalProperties.$apexcharts = true;
  app.use(VueApexCharts);
}

if (!app.config.globalProperties.$calendar) {
  app.config.globalProperties.$calendar = true;
  app.use(Calendar, {});
}
