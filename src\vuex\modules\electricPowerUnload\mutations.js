export default {
  // 開始載入
  getDataBegin(state) {
    state.loading = true;
  },

  // 載入成功 - 階段群組詳細列表
  getStageGroupDetailListSuccess(state, data) {
    state.loading = false;
    console.log('mutations 接收到的 data:', data);
    
    // 更新摘要詳細列表
    state.summaryDetailList = data.SummaryDetailList || [];
    console.log('更新後的 summaryDetailList:', state.summaryDetailList);
    
    // 更新模式選項
    state.modeCodeNamePairs = data.ModeCodeNamePairs || [];
    
    // 更新警報測點列表
    state.alarmTagList = data.UnloadAlarmTagListInSystem || [];
    
    // 更新可消耗測點列表
    state.consumableTagList = data.ElectricPowerConsumableTagListInSystem || [];
    console.log('設置的可消耗測點列表:', state.consumableTagList);
  },

  // 載入測點列表成功
  getTagListSuccess(state, data) {
    state.tagList = data;
  },

  // 載入警報測點列表成功
  getAlarmTagListSuccess(state, data) {
    state.alarmTagList = data;
  },

  // 載入失敗
  getDataErr(state, error) {
    state.loading = false;
    state.error = error;
    console.error("載入失敗:", error);
  },

  // 更新系統設定
  updateConfig(state, config) {
    state.config = { ...state.config, ...config };
  },

  // 清除錯誤
  clearError(state) {
    state.error = null;
  }
}; 