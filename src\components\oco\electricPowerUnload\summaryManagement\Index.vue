<template>
  <div>
    <sdCards title="摘要管理">
      <template #button>
        <sdButton type="primary" @click="openAddModal">
          新增摘要
        </sdButton>
      </template>

      <a-spin :spinning="loading">
        <a-table
          :data-source="tableData"
          :columns="columns"
          :pagination="false"
          row-key="SummaryId"
          size="middle"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'mode'">
              <a-tag 
                :color="getModeColor(record.Mode)"
                style="color: #000000 !important; font-weight: bold !important;"
              >
                {{ getModeText(record.Mode) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'isLoad'">
              <a-tag 
                :color="record.IsLoad ? 'green' : 'red'"
                style="color: #000000 !important; font-weight: bold !important;"
              >
                {{ record.IsLoad ? '啟用' : '停用' }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button 
                  type="link" 
                  size="small"
                  @click="openEditModal(record)"
                >
                  編輯
                </a-button>
                <a-popconfirm
                  title="確定要刪除這個摘要嗎？"
                  @confirm="handleDelete(record)"
                >
                  <a-button 
                    type="link" 
                    danger 
                    size="small"
                  >
                    刪除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-spin>
    </sdCards>

    <!-- 新增/編輯摘要 Modal -->
    <sdModal
      v-if="modalVisible"
      :visible="modalVisible"
      :title="isEdit ? '編輯摘要' : '新增摘要'"
      @cancel="closeModal"
      :width="600"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="摘要名稱" name="summaryName">
          <a-input 
            v-model:value="formState.summaryName"
            placeholder="請輸入摘要名稱"
          />
        </a-form-item>

        <a-form-item label="卸載模式" name="mode">
          <a-select
            v-model:value="formState.mode"
            placeholder="請選擇卸載模式"
            :options="modeOptions.map(mode => ({ label: mode.Name, value: mode.Id }))"
          />
        </a-form-item>

        <a-form-item label="持續秒數" name="continuedSecond">
          <a-input-number
            v-model:value="formState.continuedSecond"
            :min="0"
            style="width: 100%"
            placeholder="請輸入持續秒數"
          />
        </a-form-item>
        
        <a-form-item label="加載功能" name="isLoad">
          <a-switch 
            v-model:checked="formState.isLoad"
            checked-children="啟用"
            un-checked-children="停用"
            :disabled="false"
          />
        </a-form-item>

        <a-form-item label="可消耗測點" name="consumableTagIdList">
          <a-select
            v-model:value="formState.consumableTagIdList"
            mode="multiple"
            placeholder="請選擇可消耗測點"
            style="width: 100%"
            :options="consumableTagOptions"
            :loading="!consumableTagList.length"
          />
          <div v-if="formState.consumableTagIdList.length > 0" style="margin-top: 8px; font-size: 12px; color: #666;">
            已選擇 {{ formState.consumableTagIdList.length }} 個測點
          </div>
        </a-form-item>
      </a-form>

      <div style="text-align: right; margin-top: 16px; padding-top: 16px; border-top: 1px solid #f0f0f0;">
        <a-space>
          <a-button @click="closeModal">取消</a-button>
          <a-button type="primary" @click="handleSubmit" :loading="submitLoading">
            {{ isEdit ? '更新' : '新增' }}
          </a-button>
        </a-space>
      </div>
    </sdModal>
  </div>
</template>

<script>
import { Modal } from 'ant-design-vue';

export default {
  name: 'SummaryManagement',
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    summaryDetailList: {
      type: Array,
      default: () => []
    },
    modeOptions: {
      type: Array,
      default: () => []
    },
    consumableTagList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      modalVisible: false,
      isEdit: false,
      submitLoading: false,
      formState: {
        summaryId: null,
        summaryName: '',
        mode: null,
        continuedSecond: 0,
        isLoad: false,
        consumableTagIdList: []
      },
      rules: {
        summaryName: [
          { required: true, message: '請輸入摘要名稱', trigger: 'blur' }
        ],
        mode: [
          { required: true, message: '請選擇卸載模式', trigger: 'change' }
        ],
        continuedSecond: [
          { required: true, message: '請輸入持續秒數', trigger: 'blur' },
          { type: 'number', min: 0, message: '持續秒數必須大於等於0', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    // 表格資料
    tableData() {
      return this.summaryDetailList;
    },
    // 可消耗測點選項
    consumableTagOptions() {
      if (!this.consumableTagList || this.consumableTagList.length === 0) {
        return [];
      }
      
      return this.consumableTagList.map(tag => ({
        label: tag.Description || tag.FullTagName || tag.Name || tag.TagName || '未知測點',
        value: tag.TagId || tag.Id || tag.id
      }));
    },
    // 表格欄位定義
    columns() {
      return [
        {
          title: '摘要名稱',
          dataIndex: 'SummaryName',
          key: 'summaryName',
          width: 200
        },
        {
          title: '卸載模式',
          dataIndex: 'Mode',
          key: 'mode',
          width: 120
        },
        {
          title: '加載功能',
          dataIndex: 'IsLoad',
          key: 'isLoad',
          width: 100
        },
        {
          title: '持續秒數',
          dataIndex: 'ContinueSeconds',
          key: 'continueSeconds',
          width: 100
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          fixed: 'right'
        }
      ];
    }
  },
  methods: {
    // 取得模式顏色
    getModeColor(mode) {
      const colors = {
        0: 'blue',     // 手動模式
        1: 'green',    // 自動模式
        2: 'orange'    // 半自動模式
      };
      return colors[mode] || 'default';
    },

    // 取得模式文字
    getModeText(mode) {
      const texts = {
        0: '手動模式',
        1: '自動模式',
        2: '半自動模式'
      };
      return texts[mode] || '未知模式';
    },

    // 開啟新增 Modal
    openAddModal() {
      this.isEdit = false;
      this.resetForm();
      this.modalVisible = true;
    },

    // 開啟編輯 Modal
    openEditModal(record) {
      this.isEdit = true;
      this.resetForm();
      
      console.log('編輯記錄:', record);
      console.log('記錄的所有屬性:', Object.keys(record));
      console.log('ConcatedConsumableTagsId 內容:', record.ConcatedConsumableTagsId);
      console.log('ConcatedConsumableTagsId 類型:', typeof record.ConcatedConsumableTagsId);
      
      // 填充表單資料
      this.formState.summaryId = record.SummaryId;
      this.formState.summaryName = record.SummaryName;
      this.formState.mode = record.Mode;
      this.formState.continuedSecond = record.ContinueSeconds;
      this.formState.isLoad = record.IsLoad;
      
      // 處理可消耗測點數據 - 檢查所有可能的字段
      let consumableTagIdList = [];
      
      // 檢查所有可能的字段名稱
      const possibleFields = [
        'ConsumableTagList',
        'ElectricPowerConsumableTagList', 
        'ConsumableTagIdList',
        'ConsumableTags',
        'Tags',
        'TagList',
        'ElectricPowerConsumableTags',
        'ConcatedConsumableTagsId'
      ];
      
      for (const field of possibleFields) {
        if (record[field]) {
          if (field === 'ConcatedConsumableTagsId') {
            // 特殊處理 ConcatedConsumableTagsId，它可能是逗號分隔的字符串
            if (typeof record[field] === 'string' && record[field].trim()) {
              consumableTagIdList = record[field].split(',').map(id => id.trim()).filter(id => id);
              break;
            }
          } else if (Array.isArray(record[field])) {
            if (record[field].length > 0 && typeof record[field][0] === 'object') {
              // 如果是對象數組，提取 TagId
              consumableTagIdList = record[field].map(tag => tag.TagId || tag.Id || tag.id);
            } else {
              // 如果是 ID 數組
              consumableTagIdList = record[field];
            }
            break;
          }
        }
      }
      
      this.formState.consumableTagIdList = consumableTagIdList;
      
      this.modalVisible = true;
    },

    // 關閉 Modal
    closeModal() {
      this.modalVisible = false;
      this.resetForm();
    },

    // 重設表單
    resetForm() {
      this.formState.summaryId = null;
      this.formState.summaryName = '';
      this.formState.mode = null;
      this.formState.continuedSecond = 0;
      this.formState.isLoad = false;
      this.formState.consumableTagIdList = [];
      
      if (this.$refs.formRef) {
        this.$refs.formRef.resetFields();
      }
    },

    // 提交表單
    async handleSubmit() {
      try {
        // 驗證表單
        const validateResult = await this.$refs.formRef.validate();
        console.log('表單驗證結果:', validateResult);
        
        // 檢查必填字段
        if (!this.formState.summaryName || !this.formState.summaryName.trim()) {
          throw new Error('請輸入摘要名稱');
        }
        
        if (this.formState.mode === undefined || this.formState.mode === null) {
          throw new Error('請選擇卸載模式');
        }
        
        if (this.formState.continuedSecond === undefined || this.formState.continuedSecond === null) {
          throw new Error('請輸入持續秒數');
        }
        
        this.submitLoading = true;

        // 準備提交數據 - 確保 consumableTagIdList 是普通數組
        const submitData = {
          summaryName: this.formState.summaryName.trim(),
          mode: this.formState.mode,
          continuedSecond: this.formState.continuedSecond,
          isLoad: this.formState.isLoad,
          consumableTagIdList: Array.isArray(this.formState.consumableTagIdList) 
            ? [...this.formState.consumableTagIdList] 
            : []
        };

        console.log('準備提交的數據:', submitData);

        if (this.isEdit) {
          // 編輯操作需要 summaryId
          if (!this.formState.summaryId) {
            throw new Error('缺少摘要ID，無法進行編輯操作');
          }
          submitData.summaryId = this.formState.summaryId;
          console.log('編輯操作，summaryId:', this.formState.summaryId);
          await this.$store.dispatch('electricPowerUnload/editSummary', submitData);
          Modal.success({
            title: '更新成功',
            content: '摘要已更新'
          });
        } else {
          await this.$store.dispatch('electricPowerUnload/createSummary', submitData);
          Modal.success({
            title: '新增成功',
            content: '摘要已新增'
          });
        }

        this.closeModal();
        this.$emit('refresh');
      } catch (error) {
        console.error('提交失敗:', error);
        
        // 檢查是否是表單驗證錯誤
        if (error.errorFields && error.errorFields.length > 0) {
          const errorMessages = error.errorFields.map(field => field.errors.join(', ')).join('; ');
          Modal.error({
            title: this.isEdit ? '更新失敗' : '新增失敗',
            content: `表單驗證失敗: ${errorMessages}`
          });
        } else {
          Modal.error({
            title: this.isEdit ? '更新失敗' : '新增失敗',
            content: error.message || '操作失敗，請檢查輸入資料'
          });
        }
      } finally {
        this.submitLoading = false;
      }
    },

    // 刪除摘要
    async handleDelete(record) {
      try {
        await this.$store.dispatch('electricPowerUnload/deleteSummary', record.SummaryId);
        Modal.success({
          title: '刪除成功',
          content: '摘要已刪除'
        });
        this.$emit('refresh');
      } catch (error) {
        Modal.error({
          title: '刪除失敗',
          content: error.message || '無法刪除摘要'
        });
      }
    }
  }
};
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style> 