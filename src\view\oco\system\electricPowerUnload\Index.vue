<template>
  <div>
    <sdPageHeader title="電力加卸載管理" subtitle="Electric Power Unload Management">
      <template #extra>
        <a-space>
          <a-button @click="refreshData" :loading="loading">
            <unicon name="refresh" width="14"></unicon>
            刷新
          </a-button>
        </a-space>
      </template>
    </sdPageHeader>

    <Main>
      <a-tabs v-model="activeTab" @change="handleTabChange">
        <a-tab-pane key="summary" tab="摘要管理">
          <SummaryManagement
            :loading="loading"
            :summary-detail-list="summaryDetailList"
            :mode-options="modeOptions"
            :consumable-tag-list="consumableTagList"
            @refresh="refreshData"
          />
        </a-tab-pane>

        <a-tab-pane key="stage" tab="階段管理">
          <div v-if="summaryDetailList.length === 0" class="no-selection">
            <a-empty description="尚未建立任何摘要，請先在摘要管理中建立摘要" />
          </div>
          <div v-else>
            <!-- 摘要選擇器 -->
            <div class="summary-selector" style="margin-bottom: 16px;">
              <a-form layout="inline">
                <a-form-item label="選擇摘要">
                  <a-select
                    v-model:value="selectedSummaryId"
                    style="width: 300px"
                    placeholder="請選擇要管理的摘要"
                    @change="handleSummaryChange"
                  >
                    <a-select-option 
                      v-for="summary in summaryDetailList" 
                      :key="summary.SummaryId" 
                      :value="summary.SummaryId"
                    >
                      {{ summary.SummaryName }} ({{ getModeText(summary.Mode) }})
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-form>
            </div>

            <!-- 階段管理組件 -->
            <div v-if="selectedSummaryId">
              <StageManagement
                :loading="loading"
                :summary-id="selectedSummaryId"
                :stage-list="selectedStageList"
                :tag-list="tagList"
                :alarm-tag-list="alarmTagList"
                @stageListUpdated="refreshData"
              />
            </div>
            <div v-else class="no-selection">
              <a-empty description="請選擇一個摘要來管理階段" />
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="monitor" tab="即時監控">
          <RealTimeMonitor
            :loading="loading"
            :summary-list="summaryDetailList"
            :contract-capacity="contractCapacity"
          />
        </a-tab-pane>

        <a-tab-pane key="settings" tab="系統設定">
          <SystemSettings
            :loading="loading"
            :config="systemConfig"
            @config-updated="handleConfigUpdated"
          />
        </a-tab-pane>
      </a-tabs>
    </Main>
  </div>
</template>

<script>
import { Main } from '../../styled.js';
import SummaryManagement from '@/components/oco/electricPowerUnload/summaryManagement/Index.vue';
import StageManagement from '@/components/oco/electricPowerUnload/stageManagement/Index.vue';
import RealTimeMonitor from '@/components/oco/electricPowerUnload/realTimeMonitor/Index.vue';
import SystemSettings from '@/components/oco/electricPowerUnload/systemSettings/Index.vue';

export default {
  name: 'ElectricPowerUnload',
  components: {
    Main,
    SummaryManagement,
    StageManagement,
    RealTimeMonitor,
    SystemSettings
  },
  data() {
    return {
      activeTab: 'summary',
      selectedSummaryId: null
    };
  },
  computed: {
    // 從 state 取得資料
    loading() {
      return this.$store.state.electricPowerUnload?.loading || false;
    },
    summaryDetailList() {
      return this.$store.state.electricPowerUnload?.summaryDetailList || [];
    },
    modeOptions() {
      return this.$store.state.electricPowerUnload?.modeCodeNamePairs || [];
    },
    consumableTagList() {
      return this.$store.state.electricPowerUnload?.consumableTagList || [];
    },
    tagList() {
      return this.$store.state.electricPowerUnload?.tagList || [];
    },
    alarmTagList() {
      return this.$store.state.electricPowerUnload?.alarmTagList || [];
    },
    systemConfig() {
      return this.$store.state.electricPowerUnload?.config || {};
    },
    contractCapacity() {
      return this.systemConfig.contractCapacity || 250;
    },
    // 選中的階段列表
    selectedStageList() {
      console.log('selectedStageList 計算中...');
      if (!this.selectedSummaryId) {
        console.log('selectedSummaryId 為 null，返回空數組');
        return [];
      }
      const summary = this.summaryDetailList.find(s => s.SummaryId === this.selectedSummaryId);
      console.log('找到摘要:', summary);
      return summary?.StageDetailList || [];
    }
  },
  watch: {
    // 監聽摘要列表變化，自動選擇第一個摘要
    summaryDetailList: {
      handler(newList) {
        if (newList.length > 0 && !this.selectedSummaryId) {
          this.selectedSummaryId = newList[0].SummaryId;
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.refreshData();
  },
  methods: {
    // Tab 切換處理
    handleTabChange(key) {
      if (key === 'stage' && !this.selectedSummaryId && this.summaryDetailList.length > 0) {
        this.selectedSummaryId = this.summaryDetailList[0].SummaryId;
      }
    },

    // 刷新資料
    async refreshData() {
      try {
        await this.$store.dispatch('electricPowerUnload/getStageGroupDetailList');
        await this.$store.dispatch('electricPowerUnload/getTagList');
        await this.$store.dispatch('electricPowerUnload/getAlarmTagList');
      } catch (error) {
        console.error('刷新資料失敗:', error);
      }
    },

    // 處理設定更新
    handleConfigUpdated(newConfig) {
      console.log('系統設定已更新:', newConfig);
    },

    // 處理摘要選擇變化
    handleSummaryChange(value) {
      console.log('選擇摘要變化:', value);
      this.selectedSummaryId = value;
      console.log('selectedSummaryId 已設置為:', this.selectedSummaryId);
    },

         // 根據模式代碼獲取模式文本
     getModeText(modeCode) {
       const mode = this.modeOptions.find(m => m.Id === modeCode);
       return mode ? mode.Name : `模式${modeCode}`;
     }
  }
};
</script>

<style scoped>
.no-selection {
  text-align: center;
  padding: 40px;
}
</style> 