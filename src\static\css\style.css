/* Utilitiy classes */

.spin {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
}

.d-flex {
  display: flex;
}

.flex-grid .flex-grid-child {
  padding: 0 12px;
}

.align-center-v {
  display: flex;
  align-items: center;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-center {
  justify-content: center;
}

.align-items-center {
  align-items: center;
}

.line-height-0 {
  line-height: 0;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.status {
  display: inline !important;
  font-size: 13px;
  font-weight: 500;
  background-color: #868eae10;
  padding: 4.5px 11.85px;
  border-radius: 15px;
}

.status.Success {
  background-color: #01b81a10;
  color: #01b81a;
}

.status.warning {
  background-color: #fa8b0c10;
  color: #fa8b0c;
}

.status.error {
  background-color: #ff4d4f10;
  color: #ff4d4f;
}

ul {
  list-style: outside none none;
  margin: 0;
  padding: 0;
}

/* spacing classes */

.m-0 {
  margin: 0 !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}
.mb-25 {
  margin-bottom: 25px !important;
}
.mb-30 {
  margin-bottom: 30px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pl-0 {
  padding-left: 0 !important;
}

.mt-20 {
  margin-top: 20px !important;
}

.mt-25 {
  margin-top: 25px;
}
.mt-30 {
  margin-top: 30px;
}

.pr-0 {
  padding-right: 0 !important;
}

/* default card style */

.ant-card {
  box-shadow: 0 5px 20px #9299b803;
}

.ant-card-body {
  padding: 25px !important;
}
.ant-card-body .ant-card-body {
  padding: 0 !important;
}

.ant-card-head {
  padding-left: 25px !important;
  padding-right: 25px !important;
}

.ant-card-head-title span {
  display: inline-block;
  /* margin-left: 15px; */
  font-size: 11px;
  font-weight: 500;
  color: #868eae;
}

.ant-card-head .ant-card-extra {
  display: flex;
  align-items: center;
  padding: 0;
}

.ant-card-head .ant-card-extra a {
  color: #868eae;
}

.ant-card-head .ant-card-extra a svg {
  fill: #868eae;
}

.ant-card-extra .ant-dropdown-trigger {
  line-height: 0;
  order: 1;
  margin-left: 20px;
}

.ninjadash_unresizable {
  resize: none;
}

/* menu padding */
/* .ant-layout-sider .ant-layout-sider-children .ant-menu{
  padding-bottom: 70px;
} */

/* ninjaDash Delete Modal */
.ninjaDash-delete-modal .ant-modal-header {
  text-align: center;
  padding: 22px 24px;
}

.ninjaDash-delete-modal .ant-modal-header .ant-modal-title {
  font-size: 20px;
  color: #ff0f0f;
}
.ninjaDash-delete-modal .ant-modal-body {
  padding: 20px 24px;
}

.ninjaDash-delete-modal .ninjadash-delete-confirm {
  text-align: center;
}
.ninjaDash-delete-modal .ninjadash-delete-confirm__action a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  min-height: 32px;
  border-radius: 3px;
  color: #fff;
  background-color: #01b81a;
}
.ninjaDash-delete-modal
  .ninjadash-delete-confirm__action
  .ninjadash-delete-confirm__cancel {
  border: 1px solid #e3e6ef;
  background-color: #fff;
  margin-right: 10px;
  color: #868eae;
}

/* ant radio group */
.ant-radio-button-wrapper-checked {
  color: #fff !important;
}

/* card nav */

.card-nav ul {
  list-style: none;
  display: flex;
  margin: 0 -8px !important;
}

.card-nav ul li {
  margin: 0 8px !important;
  position: relative;
}

.card-nav ul li a {
  font-weight: 500;
  color: #868eae;
  font-size: 12px;
}

.card-nav ul li.active a {
  color: #ff8000;
  font-weight: 500;
}

.card-nav ul li.active:before {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  background: #ff8000;
  left: 0;
  bottom: -17px;
}

/* chart growth upward */

.growth-downward p,
.growth-upward p {
  font-size: 14px;
  color: #868eae;
  margin: 0;
}

.growth-downward h1 sub svg,
.growth-upward h1 sub svg {
  position: relative;
  top: 2px;
  font-size: 14px;
  font-weight: 600;
  left: 5px;
  bottom: 0;
}

/*
.growth-downward h1, .growth-upward h1 {
  font-size: 22px;
  margin: 6px 0 0;
} */

.growth-downward h1 sub {
  color: #ff4d4f;
}

.growth-upward h1 sub {
  color: #01b81a;
}

/* Chart */

.chart-label {
  display: flex;
}

.chart-label .chart-label__single {
  align-items: center;
}

.chart-label .chart-label__single:not(:last-child) {
  margin-right: 40px;
}

.chart-label .chart-label__single p {
  margin: 0;
  color: #868eae;
}

/* revenue doughnut */

.revenue-doughnut {
  display: flex;
  justify-content: center;
}

.revenue-doughnut > div {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.revenue-doughnut .rd-labels {
  width: 100%;
  margin-top: 30px;
}

.revenue-chat-percentage span {
  display: block;
}

.revenue-chat-percentage span:not(:last-child) {
  margin-bottom: 15px;
}

.revenue-chat-percentage span {
  display: block;
  font-size: 13px;
  color: #868eae;
}

.revenue-chat-percentage span:not(:last-child) {
  margin-bottom: 20px;
}

.ant-progress-status-warning .ant-progress-bg {
  background: #fa8b0c;
}

/* progress bars */

.progress-primary .ant-progress-bg {
  background: #ff8000;
}

.progress-secondary .ant-progress-bg {
  background: #ff69a5;
}

.progress-success .ant-progress-bg {
  background: #01b81a;
}

.progress-info .ant-progress-bg {
  background: #00aaff;
}

.progress-warning .ant-progress-bg {
  background: #fa8b0c;
}

.progress-danger .ant-progress-bg {
  background: #ff4d4f;
}

/* color classes */

.color-primary {
  color: #ff8000;
}

.color-secondary {
  color: #5840ff;
}

.color-info {
  color: #2c99ff;
}

.color-warning {
  color: #fa8b0c;
}

.color-success {
  color: #01b81a;
}

.color-danger {
  color: #ff4d4f;
}

.color-dark {
  color: #272b41;
}

.color-error {
  color: #f5222d;
}

.color-gray {
  color: #5a5f7d;
}

/* Button Styles */

.button-example .ant-btn {
  margin: 4px;
}

.button-example .ant-btn-group .ant-btn {
  margin: 0;
}

.ant-btn-white {
  color: #5a5f7d !important;
  border-color: #e3e6ef !important;
}

.ant-btn-white:hover {
  color: #ff8000 !important;
  background-color: #fff !important;
  border-color: #fff;
}

.ant-btn-white:focus {
  background-color: transparent !important;
}

.ant-btn-dashed {
  border-width: 1px !important;
  color: #5a5f7d !important;
}

.ant-btn-dashed:hover {
  color: #ff8000;
  border: 1px dashed #ff8000;
}

.ant-btn-primary[disabled] {
  color: #fff;
  background: #ff800060;
}

.ant-btn-light[disabled] {
  background: #fff;
}

.ant-btn-round.ant-btn-sm {
  height: 38px;
}

.ant-btn-white[disabled] {
  background-color: transparent;
  opacity: 0.6;
}

.ant-btn-white[disabled]:hover {
  color: #5a5f7d !important;
}

.ant-btn-primary[disabled]:hover {
  color: #fff !important;
  background: #ff800060 !important;
}

.btn-icon {
  padding: 0 13px;
}

.btn-inc,
.btn-dec {
  height: 38px;
  width: 38px;
  font-size: 20px;
  padding: 0 12px !important;
  border-radius: 10px !important;
  border: 0 none;
}

.btn-inc:hover,
.btn-dec:hover {
  background: #ff800010 !important;
  border: 0 none !important;
}

.btn-inc:hover i,
.btn-dec:hover i {
  color: #ff8000;
}
.btn-inc:hover svg,
.btn-dec:hover svg {
  fill: #ff8000;
}

/* input styles */

.ant-form-item-label > label {
  font-weight: 500;
}

.ant-picker-input > input::placeholder {
  color: #adb4d2 !important;
}

.ant-input-affix-wrapper,
.ant-input {
  border-radius: 6px;
}

.ant-input-affix-wrapper .ant-input-prefix {
  margin-right: 8px;
}

.ant-input-affix-wrapper-lg .ant-input-lg {
  font-size: 15px;
}

/* Calendar Styles */

.ant-picker-calendar-header .ant-select-selector {
  height: 32px !important;
}

.ant-picker-calendar-header .ant-select-selection-search-input {
  height: 30px !important;
}

.dark-mode .ant-picker-calendar {
  background: none;
}
.dark-mode .ant-picker-calendar-full .ant-picker-panel {
  background: none;
}
.dark-mode
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-calendar-date {
  border-color: #323541;
}
.dark-mode
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected
  .ant-picker-calendar-date,
.dark-mode
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected:hover
  .ant-picker-calendar-date,
.dark-mode
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected
  .ant-picker-calendar-date-today,
.dark-mode
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected:hover
  .ant-picker-calendar-date-today {
  background: #282b37;
}
.dark-mode
  .ant-picker-cell:hover:not(.ant-picker-cell-in-view)
  .ant-picker-cell-inner,
.dark-mode
  .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(
    .ant-picker-cell-range-start
  ):not(.ant-picker-cell-range-end):not(.ant-picker-cell-range-hover-start):not(
    .ant-picker-cell-range-hover-end
  )
  .ant-picker-cell-inner {
  background: #282b37;
}

.ant-select-single .ant-select-selector .ant-select-selection-item {
  line-height: 30px !important;
}

html[dir="rtl"]
  .ant-select-single.ant-select-show-arrow
  .ant-select-selection-item {
  padding-left: 19px;
}

/* pagination */

.ant-pagination
  .ant-select-single:not(.ant-select-customize-input)
  .ant-select-selector {
  height: 32px !important;
}

.ant-pagination-item-active {
  background-color: #ff8000 !important;
}

.ant-pagination-item-active a {
  color: #fff !important;
}

.ant-pagination .ant-pagination-options .ant-select-selection-item {
  font-size: 13px;
  line-height: 30px !important;
}

.ant-pagination .ant-pagination-options .ant-pagination-options-quick-jumper {
  height: 30px;
  line-height: 30px;
  margin-left: 8px;
}

.ant-pagination
  .ant-pagination-options
  .ant-pagination-options-quick-jumper
  input {
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.ant-pagination .ant-pagination-prev,
.ant-pagination .ant-pagination-next,
.ant-pagination .ant-pagination-jump-prev,
.ant-pagination .ant-pagination-jump-next,
.ant-pagination .ant-pagination-item,
.ant-pagination .ant-pagination-options .ant-select-selector {
  border: 1px solid #f1f2f6 !important;
  background-color: #fff;
}

.ant-pagination .ant-pagination-jump-prev .ant-pagination-item-ellipsis,
.ant-pagination .ant-pagination-jump-next .ant-pagination-item-ellipsis {
  color: #5a5f7d !important;
  line-height: 2.6;
}

.ant-pagination .ant-pagination-jump-prev .ant-pagination-item-link,
.ant-pagination .ant-pagination-jump-next .ant-pagination-item-link {
  display: block;
}

.ant-pagination-prev,
.ant-pagination-next {
  line-height: 28px !important;
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  border: 0 none !important;
}

.ant-pagination .ant-pagination-item a {
  color: #5a5f7d;
}

.ant-pagination
  .ant-select-single
  .ant-select-selector
  .ant-select-selection-item {
  line-height: 30px !important;
  color: #5a5f7d;
}

.ant-pagination
  .ant-select-single:not(.ant-select-customize-input)
  .ant-select-selector
  .ant-select-selection-search-input {
  height: 100% !important;
}

.ant-pagination {
  margin: -4px -4px !important;
}

.ant-pagination-options-size-changer.ant-select {
  margin-right: 0 !important;
}

.ant-pagination-item,
.ant-pagination-options,
.ant-pagination-prev,
.ant-pagination-jump-prev,
.ant-pagination-jump-next,
.ant-pagination-next {
  margin: 4px !important;
}

.ant-table-pagination {
  margin-top: 30px !important;
}

/* Wizard Modal */
.submission-successModal {
  text-align: center;
}
.submission-successModal .icon-success {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  margin: 0 auto 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  border: 1px solid #01b81a;
}
.submission-successModal p {
  margin-bottom: 0;
}
.submission-successModal .icon-success svg {
  fill: #01b81a;
}
.submission-successModal .submission-action {
  margin: 30px -5px -5px -5px;
}
.submission-successModal .submission-action button {
  margin: 5px;
  height: 38px;
}

/* Tree Select */

.ant-tree-select.ant-select-single:not(.ant-select-customize-input)
  .ant-select-selector {
  height: 42px !important;
  line-height: 40px;
}

.ant-tree-select.ant-select-single:not(.ant-select-customize-input)
  .ant-select-selector
  .ant-select-selection-search-input {
  height: 100% !important;
}

.ant-tree-select.ant-select-single
  .ant-select-selector
  .ant-select-selection-placeholder {
  line-height: 40px !important;
}

/* radio style */

.ant-radio-checked .ant-radio-inner {
  border-width: 5px !important;
}

.ant-radio-inner::after {
  content: none !important;
}

/* Statistics */

.ant-statistic .ant-statistic-title {
  color: #9299b8;
}

.ant-statistic .ant-statistic-content span {
  font-size: 20px;
}

/* Steps */

.steps-action.justify-content-center {
  justify-content: center !important;
}

/* rate */

.ant-rate-star.ant-rate-star-zero span svg {
  fill: #c6d0dc;
}

.ant-rate-star:not(:last-child) {
  margin-right: 2px !important;
}

.ant-rate-text {
  color: #5a5f7d;
}

/* result */

.ant-result-icon {
  margin-bottom: 20px !important;
}

.ant-result-title {
  font-weight: 500;
  margin-bottom: 10px;
}

.ant-result-extra {
  height: 34px;
  padding: 4px 10.72px;
}

.ant-result-content .ant-typography strong {
  font-weight: 500;
}

.ant-result-content .ant-typography:last-child {
  margin-bottom: 0;
}

.dark-mode .ant-result-subtitle {
  color: #8c90a4;
}

.dark-mode .ant-result-content .ant-typography strong {
  color: #a4a5aa;
}

/* form select */

.ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
  height: 38px !important;
}

.ant-select-single .ant-select-selector .ant-select-selection-item,
.ant-select-single .ant-select-selector .ant-select-selection-placeholder {
  line-height: 33px !important;
}

.ant-select-multiple .ant-select-selector {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
}

.ninjadash_fullwidth-select {
  width: 100%;
}

/* Nasted Comments Styles */

.nested-comment-wrapper .comment-title {
  font-size: 12px;
  padding-bottom: 10px;
  margin-bottom: 22px;
  border-bottom: 1px solid #e3e6ef;
}

/* calendar style */

.events {
  list-style: none;
  margin: 0;
  padding: 0;
}

.events .ant-badge-status {
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  text-overflow: ellipsis;
  font-size: 12px;
}

.notes-month {
  text-align: center;
  font-size: 28px;
}

.notes-month section {
  font-size: 28px;
}

/* Breadcrumb demo */

.demo {
  margin: 16px;
}

.demo-nav {
  height: 30px;
  line-height: 30px;
  margin-bottom: 16px;
  background: #f8f8f8;
}

.demo-nav a {
  line-height: 30px;
  padding: 0 8px;
}

.app-list {
  margin-top: 16px;
}

/* Modal */

.ant-modal .ant-modal-content {
  border-radius: 10px;
}

.ant-modal .ant-modal-header {
  border-radius: 10px 10px 0 0;
}

.ant-modal-body p:last-child {
  margin-bottom: 0;
}

.project-modal {
  padding: 0 5px !important;
}

.project-modal .projects-members {
  margin-top: 12px;
}

.project-modal .projects-members img {
  margin: 0 3px;
  border-radius: 50%;
}

.project-modal .ant-form-item-control-input {
  margin-top: 10px;
  min-height: auto;
}

.project-modal .ant-select-selector {
  border-color: #e3e6ef;
}

.project-modal-footer {
  padding: 10px !important;
  text-align: left !important;
}
.project-modal .ant-form-item:not(:last-child) {
  margin-bottom: 15px !important;
}
.project-modal button {
  margin-top: 15px;
}
/* Create contact modal */
.create-contact-modal {
  padding: 0 !important;
}
.create-contact-modal .ant-form-item-label {
  line-height: 35px;
}
.create-contact-modal .ant-form-item:not(:last-child) {
  margin-bottom: 15px !important;
}

/* Export Modal */
.export-modal .ant-form-item-control-wrapper {
  width: 100%;
}
.export-modal .ant-form-item-control-wrapper .ant-input {
  padding: 7px 11px !important;
}
.export-modal .btn-export {
  height: 42px;
  margin-right: 10px;
}
.export-modal .btn-cancel {
  height: 44px;
  font-size: 14px;
}
.export-modal .ant-form-item {
  margin-bottom: 15px;
}
.export-modal .ant-input {
  height: 38px;
}

/*  */
.ninjadash_addTask-modal {
  max-width: 390px;
}
.ninjadash_addTask-modal .ant-modal-header {
  border-bottom: 0 none;
}
.ninjadash_addTask-modal .ant-modal-header .ant-modal-title {
  font-size: 16px;
  font-weight: 500;
  color: #272b41;
}
.ninjadash_addTask-modal .ninjadash_addTask-modal-inner .ant-form-item {
  margin-bottom: 15px;
}
.ninjadash-modal-actions button {
  margin: 5px;
}
.ninjadash_addTask-modal .ant-modal-header .ant-modal-close-x svg {
  fill: #9299b8;
}
.ninjadash_addTask-modal .ant-form-item-control-input {
  min-height: 36px;
}
.ninjadash_addTask-modal input::placeholder,
.ninjadash_addTask-modal textarea::placeholder {
  color: #9299b8;
}
.ninjadash_addTask-modal input,
.ninjadash_addTask-modal textarea {
  padding: 6px 20px;
  border-radius: 5px;
}
.ninjadash_addTask-modal textarea {
  resize: none;
  min-height: 125px;
}
.ninjadash_addTask-modal .ninjadash-modal-actions {
  display: flex;
  justify-content: flex-end;
  margin: -6px;
}
.ninjadash_addTask-modal .ninjadash-modal-actions button {
  font-size: 14px;
  margin: 6px;
  height: 38px;
  border-radius: 5px;
}
/* slider */

.ant-slider-handle {
  margin-top: -6px !important;
}

/* tags */

.ant-tag {
  font-weight: 600;
  padding: 0 9.5px;
}

/* tabs */

.ant-tabs-tab span {
  display: flex;
  align-items: center;
}

.dark-mode .ant-tabs-top > .ant-tabs-nav::before,
.dark-mode .ant-tabs-bottom > .ant-tabs-nav::before,
.dark-mode .ant-tabs-top > div > .ant-tabs-nav::before,
.dark-mode .ant-tabs-bottom > div > .ant-tabs-nav::before {
  border-bottom-color: #323541;
}

/* list */

.ant-list-bordered {
  /* border-color: #e3e6ef !important; */
}

.ant-list-item-meta {
  padding: 10px 24px;
  border-bottom: 1px solid #f1f2f6;
}

.dark-mode
  .ant-list-split.ant-list-something-after-last-item
  .ant-spin-container
  > .ant-list-items
  > .ant-list-item:last-child {
  border-bottom: 1px solid #323541;
}

/* popover */

.ant-popover {
  position: fixed;
  z-index: 99999;
}

.ant-popover-inner {
  padding: 15px;
  box-shadow: 0 5px 20px #9299b820;
  border-radius: 0 0 6px 6px;
}

.ant-popover-inner .ant-popover-title {
  padding: 5px 10px 10px;
}

.ant-popover-inner .ant-popover-inner-content a {
  color: #5a5f7d;
}

/* Drawer */

.ant-drawer {
  z-index: 99999;
}

.ant-drawer .ant-input {
  padding: 7px 11px;
}

/* Select Dropdwon */

.ant-select-dropdown {
  padding: 18px 0 !important;
  box-shadow: 0 5px 20px #9299b820 !important;
  border-radius: 0 0 6px 6px !important;
}

.ant-select-item {
  min-height: 20px !important;
  padding: 4px 12px !important;
}

.ant-select-item-group {
  color: #9299b8;
}

.ant-select-item.ant-select-item-option-grouped {
  padding-left: 25px !important;
}

.ant-select-dropdown .ant-select-item.ant-select-item-option-active {
  background: #ff800005;
}

.ant-select-dropdown
  .ant-select-item.ant-select-item-option-selected
  .ant-select-item-option-content {
  padding-left: 10px;
}

.ant-select-dropdown .ant-select-item.ant-select-item-option-selected {
  color: #ff8000;
  background: #ff800006;
}

.ant-select-dropdown
  .ant-select-item.ant-select-item-option-selected
  .ant-select-item-option-content {
  color: #ff8000;
  font-weight: 500;
}

.ant-select-dropdown .ant-select-item .ant-select-item-option-content {
  transition: 0.3s;
  color: #5a5f7d;
}

/* mail props dropdown */

.mail-props {
  padding: 15px 25px;
  border: 0 none;
  background: #fff;
  border-radius: 4px;
}

.mail-props li {
  display: flex;
  margin-bottom: 12px;
}

.mail-props li span:first-child {
  min-width: 45px;
}

.mail-props li span:last-child {
  color: #5a5f7d;
}

.mail-props li:last-child {
  margin-bottom: 0;
}

.mail-props li span {
  color: #9299b8;
}

/* Basic Dropdwon */

.ant-dropdown {
  box-shadow: 0 5px 30px #9299b820 !important;
  background: none;
}

.ant-dropdown.wide-dropdwon {
  min-width: 140px !important;
}

.ant-dropdown.wide-dropdwon.kanbanCard-more {
  min-width: 220px !important;
  box-shadow: 0 17px 20px #9299b820;
}
.ant-dropdown.wide-dropdwon.kanbanCard-more a {
  padding: 10px 24px;
}

.ant-dropdown-menu {
  min-width: 200px;
  border-radius: 0 0 5px 5px;
  box-shadow: 0 0;
  background: unset !important;
}

.ant-dropdown-menu li {
  color: #5a5f7d;
  padding: 5px 25px;
}

.ant-dropdown-menu li:hover {
  background-color: #ff800005;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__title {
  background: #f4f5f7;
  width: 100%;
  margin-bottom: 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  min-height: 50px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.ninjadash-top-dropdown {
  width: 340px;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__nav {
  overflow: hidden;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__nav.notification-list {
  padding: 0 10px;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__nav li {
  width: 100%;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__nav li a {
  padding: 13px 10px;
  position: relative;
  width: 100%;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__nav li:last-child {
  margin: 0;
}

.ninjadash-top-dropdown a.btn-seeAll {
  position: relative;
  width: calc(100% + 30px);
  left: -15px;
  right: -15px;
  height: calc(100% + 15px);
  bottom: -15px;
  text-align: center;
  font-size: 13px;
  font-weight: 500;
  color: #ff8000;
  padding: 15px 0;
  border-radius: 0 0 6px 6px;
  background: #fff;
  justify-content: center;
}

.ninjadash-top-dropdown a.btn-seeAll:before {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 -15px 20px #9299b808;
  z-index: 1;
  content: "";
}

.ninjadash-top-dropdown
  .ninjadash-top-dropdown__nav
  li
  a.btn-seeAll:hover:after {
  box-shadow: 0 0;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__nav li a:hover {
  background: #fff;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__nav li a:hover:after {
  opacity: 1;
  visibility: visible;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__nav li a:hover figcaption h1 {
  color: #ff8000;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__nav li a:after {
  position: absolute;
  left: -15px;
  right: -15px;
  top: 0;
  width: calc(100% + 30px);
  height: 100%;
  box-shadow: 0 15px 50px #9299b820;
  z-index: 1;
  content: "";
  opacity: 0;
  visibility: hidden;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__content {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__content .notification-icon {
  width: 39.2px;
  height: 32px;
  margin-right: 15px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ninjadash-top-dropdown
  .ninjadash-top-dropdown__content
  .notification-icon.bg-primary {
  background: #ff800015;
  color: #ff8000;
}

.ninjadash-top-dropdown
  .ninjadash-top-dropdown__content
  .notification-icon.bg-secondary {
  background: #ff69a515;
  color: #ff69a5;
}

.ninjadash-top-dropdown
  .ninjadash-top-dropdown__content
  .notification-icon
  svg {
  width: 18px;
  height: 18px;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__content .notification-content {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ninjadash-top-dropdown .notification-text h1 {
  font-size: 14px;
  font-weight: 400;
  color: #5a5f7d;
  margin-bottom: 4px;
}

.ninjadash-top-dropdown .notification-text h1 span {
  color: #ff8000;
  font-weight: 500;
  padding-left: 0;
}

.ninjadash-top-dropdown .notification-text p {
  font-size: 12px;
  color: #adb4d2;
  margin-bottom: 0;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__content img {
  max-width: 40px;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__content figcaption {
  margin: -4px 15px 0;
}

.ninjadash-top-dropdown
  .ninjadash-top-dropdown__content
  figcaption
  .ant-badge-count {
  font-size: 8px;
  min-width: 16px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  border-radius: 8px;
}

.ninjadash-top-dropdown
  .ninjadash-top-dropdown__content
  figcaption
  p.ant-scroll-number-only-unit.current {
  height: 15px;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__content figcaption h1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__content figcaption h1 span {
  font-size: 12px;
  font-weight: 400;
}

.ninjadash-top-dropdown .ninjadash-top-dropdown__content figcaption p {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.add-file-dropdown a {
  font-size: 14px;
  font-weight: 400;
}
.add-file-dropdown a svg {
  width: 14px;
}

.folder-dropdown {
  min-width: 200px !important;
}

/* Tooltip Styles */

.ant-tooltip .ant-tooltip-inner {
  min-height: 40px;
  padding: 6px 20px;
  font-size: 14px;
  color: #5a5f7d;
  background-color: #fff;
  border-radius: 3px;
  display: flex;
  align-items: center;
  border: 1px solid #f1f2f6;
  box-shadow: 0 8px 15px #9299b815;
}

.ant-tooltip .ant-tooltip-arrow {
  /* position: relative; */
}

.ant-tooltip .ant-tooltip-arrow:after {
  position: absolute;
  left: 50%;
  top: 12px;
  transform: translateX(-50%);
  width: 13px;
  height: 13px;
  background: #fff;
  content: "";
}

.ant-tooltip .ant-tooltip-arrow .ant-tooltip-arrow-content {
  width: 10px;
  height: 10px;
  background: #fff;
}

.ant-tooltip-placement-bottom .ant-tooltip-arrow .ant-tooltip-arrow-content {
  border: 1px solid #f1f2f6;
  box-shadow: 0 0;
}

.ant-tooltip-placement-bottom .ant-tooltip-arrow {
  top: -4.071068px;
}

/* Badge Styles */

.ant-badge.badge-success .ant-badge-count {
  background: #01b81a;
}

.badge {
  font-size: 11px;
  font-weight: 500;
  padding: 0 6.5px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
}

.badge.badge-primary {
  background: #ff800015;
  color: #ff8000;
}
.ant-badge-count {
  box-shadow: none !important;
}

.dark-mode .pricing-badge {
  color: #fff !important;
}

/* Cascade Styles */
.custom-cascade-render .ant-cascader-picker {
  width: 100%;
}

.ant-cascader-picker input::placeholder {
  color: #adb4d2;
}

/* Comment Styles */

.ant-comment-actions li {
  margin-bottom: 0 !important;
}

.ant-comment-actions {
  display: flex;
  align-items: center;
}
.ant-comment-actions li > span {
  display: flex;
  align-items: center;
}
.ant-comment-actions li svg {
  fill: #adb4d2;
  margin-right: 4px;
}

/* Radio Vertical Button */

.ant-radio-vertical .ant-radio-wrapper {
  display: block;
}

.ant-radio-vertical .ant-radio-wrapper:not(:last-child) {
  margin-bottom: 14px;
}

/* Select Tree */

.ant-select-tree-list .ant-select-tree-node-content-wrapper:hover {
  background-color: #bae7ff !important;
}

.ant-select-tree-list .ant-select-arrow svg {
  font-size: 10px;
}

.ant-tree-select.ant-select-multiple .ant-select-selection-item {
  border: 0 none;
  border-radius: 3px;
  background: #f4f5f7;
  color: #5a5f7d;
  font-weight: 500;
}

/* Ant Switch */

.ant-switch:after {
  width: 14px !important;
  height: 14px !important;
}

.ant-switch-small:after {
  width: 10px !important;
  height: 10px !important;
}

/* Time Picker */

.ant-picker {
  min-width: 250px;
}

/* Input Affix */

.ant-input-affix-wrapper > input.ant-input {
  padding-left: 5px !important;
}
.ant-input-affix-wrapper > input.ant-input:focus {
  outline: 0 !important;
}

.ant-input-affix-wrapper .ant-input-prefix svg {
  fill: #e3e6ef;
}

.ant-input {
  padding: 12px 11px;
}

/* Space Item */

.ant-space-item .ant-btn span {
  font-size: 14px;
}

/* Pop confirm */

.pop-confirm .ant-btn {
  max-width: 90px;
  padding: 0px 35.5px;
  height: 44px;
}

.pop-confirm-top {
  margin-bottom: 10px;
}

.pop-confirm-bottom {
  margin-top: 10px;
}

.pop-confirm-top .ant-btn:not(:last-child),
.pop-confirm-bottom .ant-btn:not(:last-child) {
  margin-right: 10px;
}

.pop-confirm-left .ant-btn:not(:last-child),
.pop-confirm-right .ant-btn:not(:last-child) {
  margin-bottom: 10px;
}

/* Ant Upload */

.ant-upload .ant-btn {
  font-size: 14px;
  border-radius: 5px;
}

.ant-upload.ant-upload-select-picture-card {
  border-color: #e3e6ef !important;
  border-radius: 5px !important;
  background-color: #f8f9fb !important;
}

.ant-upload.ant-upload-select-picture-card .anticon {
  margin-bottom: 8px;
}

.ant-upload .anticon svg {
  fill: #9299b8;
}

.ant-upload-list {
  margin-top: 10px !important;
}

.ninjadash_upload-basic .ant-upload.ant-upload-select {
  width: 100%;
  border-radius: 4px;
}
.ninjadash_upload-basic .ant-upload.ant-upload-select .ant-upload {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.ninjadash_upload-basic .ninjadash_upload-text {
  font-size: 14px;
  color: #9299b8;
  padding-left: 15px;
}
html[dir="rtl"] .ninjadash_upload-basic .ninjadash_upload-text {
  padding-left: 0px;
  padding-right: 15px;
}
.ninjadash_upload-basic .ninjadash_upload-browse {
  font-size: 14px;
  color: #9299b8;
  display: inline-block;
  padding: 14px 23px;
  border-left: 1px solid #e3e6ef;
}
html[dir="rtl"] .ninjadash_upload-basic .ninjadash_upload-browse {
  border-left: 0 none;
  border-right: 1px solid #e3e6ef;
}
.ant-card.ninjadash_upload-form .ant-card-body {
  padding-bottom: 15px !important;
}
/* Ant Picker */

.ant-picker {
  border-color: #e3e6ef !important;
}

/* Ant Dropdown */

.ant-dropdown {
  box-shadow: 0 5px 20px #9299b820;
  background: #fff;
  border-radius: 6px;
  padding: 0 !important;
  border: 0 none;
}

.ant-dropdown a {
  display: flex;
  align-items: center;
  padding: 8px 24px;
  font-weight: 400;
  color: #5a5f7d;
}
.ant-dropdown a:hover {
  color: #ff8000;
}

.ant-dropdown a i,
.ant-dropdown a svg,
.ant-dropdown a img {
  margin-right: 8px;
}

.ant-dropdown a svg {
  fill: #9299b8;
}

.ant-dropdown div {
  box-shadow: 0 0;
  border-radius: 5px;
}

/* Picker Under Input */

.ant-form-item-control-input .ant-picker {
  padding: 0 12px 0;
  min-height: 40px;
}

/* Leaflet COntainer */

.leaflet-container {
  z-index: 0;
}

/* Table Bordered*/
.ant-table-tbody > tr.ant-table-row:hover > td,
.ant-table-tbody > tr > td.ant-table-cell-row-hover {
  background: transparent !important;
}
/* .table-bordered .ant-table-tbody > tr.ant-table-row:hover > td {
  background: #f8f9fb;
} */

.table-bordered .ant-table-thead > tr > th {
  background: #fff;
  border-top: 1px solid #f1f2f6;
}

.table-bordered .ant-table-tbody > tr > td {
  border-color: unset;
}

.table-bordered .ant-table-thead tr th,
.table-bordered .ant-table-tbody tr td {
  padding: 16px 25px;
}

.table-bordered .ant-table-thead tr th:last-child,
.table-bordered .ant-table-tbody tr td:last-child {
  text-align: right;
}

.dark-mode .ant-table-thead > tr > th {
  background: #282b37;
  color: #fff;
}
.dark-mode .ant-table-tbody > tr > td {
  background: #1b1e2b;
  color: #a4a5aa;
}

.dark-mode .ant-table-thead > tr > th,
.dark-mode .ant-table-tbody > tr > td {
  border-bottom-color: #323541;
}

/* Full Width Table */

.full-width-table .ant-card-body {
  padding: 0 !important;
}

.full-width-table .ant-table {
  border-radius: 10px !important;
}

.full-width-table .top-seller-table {
  min-height: 406px;
}
.full-width-table .top-seller-table.table-recent-orders {
  min-height: 390px;
}
.full-width-table .top-seller-table .ant-table-content th,
.full-width-table .top-seller-table .ant-table-content td {
  white-space: normal;
  padding: 16px 15px;
}

.full-width-table .top-seller-table .ant-table-content .th:first-child,
.full-width-table .top-seller-table .ant-table-content .td:first-child {
  padding-left: 25px;
}

.full-width-table .top-seller-table .ant-table-content .th:last-child,
.full-width-table .top-seller-table .ant-table-content .td:last-child {
  padding-right: 25px;
}

.full-width-table .top-seller-table th {
  text-align: right;
  color: #272b41;
  font-weight: 500;
}

.full-width-table .top-seller-table td {
  color: #5a5f7d;
}

.full-width-table .top-seller-table .ant-table-tbody > tr > td {
  text-align: right;
}

.full-width-table .top-seller-table th:first-child,
.full-width-table .top-seller-table td:first-child {
  text-align: left !important;
}

/* Table Basic */
table tbody > tr > th,
table tbody > tr > td {
  position: relative;
  padding: 16px 16px;
  overflow-wrap: break-word;
}
table thead > tr > th::before {
  content: none !important;
}

/* .ant-table-content .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td{
  background: #fafafa;
} */

/* Table Responsive */

.table-responsive .ant-table-content {
  display: block;
  width: 100%;
  overflow-x: auto;
}

.table-responsive .ant-table-content .ant-table-cell,
.table-responsive .ant-table-content tbody td,
.table-responsive .ant-table-body td {
  white-space: nowrap;
}

/* Rich TextEditor  */

.RichTextEditor__root___2QXK- {
  border: 0 none !important;
}

.RichTextEditor__root___2QXK- .EditorToolbar__root___3_Aqz {
  margin: 0;
  border-color: #f1f2f6;
}

.RichTextEditor__root___2QXK- .ButtonWrap__root___1EO_R button {
  padding: 0;
  border: 0 none;
  background: #fff;
  margin-right: 8px;
}

.RichTextEditor__root___2QXK- .Dropdown__root___3ALmx .Dropdown__value___34Py9 {
  border: 0 none;
}

.RichTextEditor__root___2QXK- .Dropdown__root___3ALmx select {
  border-right-width: 0px;
}

.RichTextEditor__editor___1QqIU .DraftEditor-editorContainer {
  border: 0 none;
}

/* ChatBox Dropdwon */

.ninjadash-chatbox__messageControl {
  min-width: 210px;
}

.ninjadash-chatbox__messageControl ul li a {
  padding: 4px 24px;
}

.ninjadash-chatbox__emoji {
  margin: -4.48px 0;
  padding: 0 10px;
}

.ninjadash-chatbox__emoji ul {
  display: flex;
  align-items: center;
}

.ninjadash-chatbox__emoji ul li {
  display: inline-block;
}

.ninjadash-chatbox__emoji ul li a {
  display: block;
  font-size: 20px;
  padding: 4px 7px;
  background: #fff;
}

.ninjadash-chatbox__emoji ul li a:hover {
  background-color: transparent;
}

.ninjadash-chatbox__emoji ul li a svg {
  margin: 0;
}

.rdrMonths {
  flex-wrap: wrap;
}

.ant-space {
  flex-wrap: wrap;
}

.ant-menu .ant-menu-submenu .ant-menu-submenu-title {
  display: flex;
  align-items: center;
}

.ant-menu-submenu.ant-menu-submenu-inline .ant-menu-submenu-title,
.ant-menu-item {
  display: inline-flex;
}

.ant-menu-item a {
  color: #404040 !important;
}

.ant-menu-submenu-popup {
  z-index: 105;
}

@media (max-width: 1150px) {
  .ant-menu-submenu-popup {
    display: none;
  }
}

.ant-menu-submenu-popup .ant-menu-sub {
  padding: 8px 0;
}

.ant-menu-submenu-popup
  .ant-menu-submenu.ant-menu-submenu-inline
  .ant-menu-submenu-title,
.ant-menu-submenu-popup .ant-menu-item {
  display: block;
}

.ant-menu-submenu-popup .ant-menu-item {
  margin-bottom: 0 !important;
}
.ant-menu-sub.ant-menu-vertical {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.ant-menu-sub.ant-menu-vertical .ant-menu-item a {
  color: #5a5f7d;
}

/* page heading */

.ant-layout-content .ant-page-header {
  padding: 28px 30px 28px 30px;
}

.ant-page-header.header-boxed {
  padding: 26px 180px 26px 180px !important;
}

.ant-page-header-heading {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}

.ant-page-header-heading-extra {
  margin: 4px 0 4px auto !important;
}
.ant-page-header-heading-extra button svg {
  fill: #fff;
  margin-right: 5px;
}

.ant-page-header-heading-title {
  text-transform: capitalize;
}

/* Antd drag & drop */

.row-dragging {
  /* background: #fafafa; */
  /* border: 1px solid #ccc; */
  box-shadow: 0 15px 50px #9299b820;
  display: flex;
}

.row-dragging tr {
  box-shadow: 0 15px 50px #9299b820;
}

.row-dragging td {
  padding: 16px;
  color: #000;
  position: relative;
  z-index: 9999;
  opacity: 0.5;
  vertical-align: middle;
}

.row-dragging td .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #01b81a;
  border-color: #01b81a;
}

.row-dragging td .todos-action,
.row-dragging td .table-actions {
  display: none;
}

.row-dragging td .user-info figcaption {
  display: none;
}

.row-dragging td .feather-move,
.row-dragging td .drag_email,
.row-dragging td .drag_company,
.row-dragging td .drag_designation,
.row-dragging td .drag_join-date,
.row-dragging td .active {
  display: inline-block;
  margin-top: 10px;
}

.row-dragging .drag-visible {
  visibility: visible;
}

/* File Export Import Modal */
.ninjadash_export-wrap .ant-modal {
  width: 390px !important;
}
.ninjadash_export-wrap .ant-modal .ant-modal-header {
  border-bottom: 0 none;
  padding: 18px 30px 0 30px;
}
.ninjadash_export-wrap .ant-modal .ant-modal-body {
  padding: 25px 30px 30px 30px;
}
.ninjadash_export-wrap .ant-modal .ninjadash-button-grp {
  text-align: right;
}
.ninjadash_export-wrap .ant-modal .ninjadash-button-grp {
  margin: 20px -8px -8px -8px;
}
.ninjadash_export-wrap .ant-modal .ninjadash-button-grp button {
  font-size: 14px;
  font-weight: 500;
  text-align: right;
  height: 40px;
  padding: 0 16px;
  margin: 8px;
}
.ninjadash_export-wrap .ant-modal .ninjadash-button-grp button.ant-btn-white {
  color: #9299b8 !important;
}
.ninjadash_export-wrap .ant-form-item-control-input {
  min-height: 36px;
}

.ninjadash_export-wrap .ant-form-item-control-input input {
  font-size: 14px;
  font-weight: 400;
  padding: 6px 20px;
  border-radius: 5px;
  color: #9299b8;
}

.ninjadash_export-wrap .ant-select-single {
  width: 100% !important;
}

.ninjadash_export-wrap .ant-form .ant-form-item {
  margin-bottom: 15px;
}

.ninjadash_export-wrap .ant-select-single .ant-select-selector {
  padding: 0 20px;
  border-color: #e3e6ef !important;
}

.ninjadash_create-file .ninjadash-button-grp {
  text-align: right;
}

.ninjadash_create-file .ant-modal {
  width: 390px !important;
}
.ninjadash_create-file .ant-modal .ant-form-item {
  margin-bottom: 20px;
}
.ninjadash_create-file .ant-modal-header {
  border-bottom: 0 none;
  padding-bottom: 6px;
}
.ninjadash_create-file .ninjadash-button-grp button {
  height: 40px;
  border-radius: 5px;
  margin: 5px;
}

.ninjadash_create-file .ant-form-item-control-input {
  border-radius: 5px;
}

.ninjadash_create-file .ant-form-item-control-input input {
  border: 1px solid #e3e6ef;
}

/* Task Modal */
.ninjadash_task-details .ant-modal-content .ant-modal-close {
  top: 10px;
}
.ninjadash_task-details .ant-modal-header {
  border-bottom: 0 none;
  padding: 30px 30px 0;
}
.ninjadash_task-details .ant-modal {
  width: 600px !important;
}
.ninjadash_task-details .ant-modal-title h1 {
  font-size: 20px;
  font-weight: 500;
  color: #272b41;
  margin-bottom: 4px;
  margin-top: 10px;
}
.ninjadash_task-details .ant-modal-title .sub-text {
  font-size: 14px;
  font-weight: 400;
  color: #868eae;
}
.ninjadash_task-details .ninjadash_task-details-modal {
  margin-top: 20px;
}
.ninjadash_task-details
  .ninjadash_task-details-modal
  .ninjadash_task-details__label {
  font-size: 16px;
  display: block;
  margin-bottom: 8px;
}
.ninjadash_task-details
  .ninjadash_task-details-modal
  .ninjadash_task-details-modal__description
  textarea {
  padding: 10px 20px;
  min-height: 88px;
  width: 100%;
  border: 0 none;
  border-radius: 4px;
  background-color: #f4f5f7;
  resize: none;
}
.ninjadash_task-details
  .ninjadash_task-details-modal
  .ninjadash_task-details-modal__description
  textarea:focus {
  outline: none;
}
.ninjadash_task-details
  .ninjadash_task-details-modal
  .ninjadash_task-details-modal__description
  textarea::placeholder {
  color: #5a5f7d;
  font-size: 15px;
}
.ninjadash_task-details .ant-modal-body {
  padding: 14px 30px 30px 30px;
}
.ninjadash_task-details .ant-modal-body .ninjadash_checklist-block {
  margin-top: 26px;
}

.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-block
  .addChecklist-wrap {
  position: relative;
}

.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-block
  .addChecklist-form {
  position: absolute;
  width: 240px;
  padding: 18px;
  left: 0;
  top: 50px;
  box-shadow: 0 15px 30px #9299bb30;
  background-color: #fff;
  border: 1px solid #e3e6ef;
  border-radius: 6px;
  z-index: 222;
}

html[dir="rtl"]
  .ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-block
  .addChecklist-form {
  left: auto;
  right: 0;
}

.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-block
  .addChecklist-form
  .add-checklist {
  padding: 10px;
  border-radius: 4px;
  background-color: #fff;
  width: 100%;
  height: 38px;
  border: 1px solid #e3e6ef;
}

.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-block
  .addChecklist-form
  .addChecklist-form-action {
  margin-top: 15px;
}

.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-block
  .addChecklist-form
  .addChecklist-form-action
  a {
  position: relative;
  top: 3px;
  display: inline-flex;
  align-items: center;
  line-height: 1;
}
.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-block
  .addChecklist-form
  .addChecklist-form-action
  a
  svg {
  fill: #9299bb;
}
.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-block
  .addChecklist-form
  .addChecklist-form-action
  .btn-add {
  margin-right: 15px;
}
html[dir="rtl"]
  .ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-block
  .addChecklist-form
  .addChecklist-form-action
  .btn-add {
  margin-left: 15px;
  margin-right: 0;
}

.ninjadash_task-details .ant-modal-body .ninjadash_checklist-block button {
  height: 38px;
  padding: 0px 18.37px;
}

.ninjadash_task-details .ant-modal-body .ninjadash_checklist-row {
  margin-top: 26px;
}
.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-row
  .ninjadash_checklist-item:not(:last-child) {
  margin-bottom: 30px;
}

.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-row
  .ninjadash_checklist-item__top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-row
  .ninjadash_checklist-item__top
  button {
  padding: 0px 16.32px;
  color: #404040;
  background-color: #f4f5f7;
}

.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-row
  .ninjadash_checklist-item__title {
  font-size: 16px;
  font-weight: 500;
  color: #272b41;
  margin-bottom: 0;
}

.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-row
  .ninjadash_checklist__progress
  .ant-progress-inner
  .ant-progress-bg {
  height: 5px !important;
  background-color: #01b81a;
}
.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-row
  .ninjadash_checklist__progress
  .ant-progress
  > div {
  position: relative;
  display: inline-flex;
  align-items: center !important;
  width: 100%;
}
.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-row
  .ninjadash_checklist__progress
  .ant-progress
  .ant-progress-text {
  font-size: 12px;
  font-weight: 500;
  order: -1;
  margin: 0 0 0 10px;
}
html[dir="rtl"]
  .ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-row
  .ninjadash_checklist__progress
  .ant-progress
  .ant-progress-text {
  margin: 0 0 0 10px;
}
.ninjadash_task-details .ant-modal-body .ninjadash_checklist-tasks {
  margin: 5px 0 5px 0;
}
.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-tasks
  .ant-checkbox-wrapper
  .ant-checkbox-inner {
  width: 18px;
  height: 18px;
  border-color: #c6d0dc;
}
.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-tasks
  .ant-checkbox-wrapper
  .ant-checkbox-checked
  .ant-checkbox-inner {
  background-color: #01b81a;
}
.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-tasks
  .ant-checkbox-wrapper
  .ant-checkbox-checked:after {
  border-color: #01b81a;
}

.ninjadash_task-details
  .ant-modal-body
  .ninjadash_checklist-item
  .ninjadash_checklist-tasks-wrap
  button {
  padding: 0px 35px;
  color: #404040;
  background-color: #f4f5f7;
}

.ninjadash_task-details .ant-modal-body {
  max-height: 800px;
  overflow-y: auto;
  overflow-x: hidden;
}

@media (max-width: 767px) {
  .ant-page-header {
    padding: 26px 15px 26px 15px !important;
  }
}

.page-header-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  margin: -6px -4px;
}

.page-header-actions button {
  font-size: 12px;
  font-weight: 500;
  height: 34px;
  padding: 0 11.5px;
  border-radius: 3px;
  box-shadow: 0 3px 5px #9299b805;
  margin: 6px 4px;
}

.page-header-actions button.ant-btn-primary i {
  margin-right: 5px;
}

.page-header-actions button.ant-btn-white:focus {
  background-color: #fff !important;
}

.page-header-actions button + button {
  margin-left: 4px;
}

.page-header-actions button.ant-btn-white svg {
  width: 12px;
  height: 12px;
  margin-right: 2px;
  fill: #ff8000;
}

/* Layout Css */

.ant-menu-dark .ant-menu-inline.ant-menu-sub,
.ant-menu.ant-menu-dark,
.ant-menu-dark .ant-menu-sub {
  background: #272b41 !important;
}

.ant-menu-dark .ant-menu-item a {
  font-weight: 400;
  color: rgba(255, 255, 255, 0.65) !important;
}
.ant-menu-dark .ant-menu-item a span {
  color: rgba(255, 255, 255, 0.65) !important;
}
.ant-menu-dark .ant-menu-item a:hover {
  color: #ffffff;
}

.ant-menu-dark .ant-menu-submenu span {
  color: rgba(255, 255, 255, 0.65) !important;
}

.ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: #ff800015 !important;
}

.ant-menu-inline-collapsed-tooltip a {
  color: #000 !important;
}

.ant-menu.ant-menu-dark
  .ant-menu-submenu-title
  .ant-menu-submenu-arrow::before {
  background: transparent;
}

/* Chart Label */

.chart-label {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
  color: #5a5f7d;
}

.chart-label .label-dot {
  margin-right: 8px;
  width: 7px;
  height: 7px;
  border-radius: 50%;
}

.chart-label .label-dot.dot-success {
  background: #01b81a;
}

.chart-label .label-dot.dot-info {
  background: #ff8000;
}

.chart-label .label-dot.dot-warning {
  background: #fa8b0c;
}

/* NOtification CSS */

.ant-notification {
  z-index: 99999 !important;
}

.rdrInputRange {
  padding-left: 10px !important;
}

/* Overlay Dark */
.overlay-dark {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #10122130;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
}
.overlay-dark.show {
  z-index: 999;
  opacity: 1;
  visibility: visible;
}

.ant-page-header-heading-extra {
  margin: 4px 0;
}

/* Performance - Region table */
.region-list-table .ant-card-body {
  padding: 25px !important;
}

/* mail editor */
.ck-editor .ck.ck-toolbar {
  border: 1px solid rgb(241, 242, 246);
}
.ck-editor .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
  border-color: rgb(241, 242, 246);
}
.ck-editor .ck.ck-editor__main > .ck-editor__editable {
  min-height: 275px;
  max-height: 275px;
}

/* vector map */
.svgMap-map-wrapper {
  background: #fff !important;
}
.svgMap-tooltip {
  background: #272b41 !important;
  border-radius: 3px !important;
  box-shadow: 0 10px 15px #272b4115 !important;
  border-bottom: 0 none !important;
  text-align: center;
  color: #fff;
}

.svgMap-tooltip .svgMap-tooltip-content {
  color: #fff !important;
}
.svgMap-tooltip .svgMap-tooltip-content table td span {
  color: #ff8000 !important;
}

/* Vector map hover fill color */
.svgMap-map-wrapper .svgMap-country.svgMap-active,
.svgMap-map-wrapper .svgMap-country:hover {
  stroke: transparent !important;
  fill: #ff8000;
}

/* Responsive CSS */

@media only screen and (max-width: 1599px) {
  .ant-page-header.header-boxed {
    padding: 26px 130px 26px 130px !important;
  }
}
@media only screen and (max-width: 1500px) {
  .full-width-table .revenue-table {
    min-height: 100%;
  }
  .pop-confirm .ant-btn {
    padding: 0 20px;
    max-width: 60px;
  }
  .pop-confirm.pop-confirm-right {
    margin-left: 300px !important;
  }
  .pop-confirm.pop-confirm-bottom,
  .pop-confirm.pop-confirm-top {
    margin-left: 80px !important;
  }
}

@media only screen and (max-width: 1399px) {
  .ant-page-header.header-boxed {
    padding: 26px 50px 26px 50px !important;
  }
}

@media only screen and (max-width: 1199px) {
  .ant-page-header {
    padding: 0 15px;
  }
}

@media only screen and (max-width: 991px) {
  .ant-page-header.header-boxed {
    padding: 26px 30px 26px 30px !important;
  }
  .rdrMonths .rdrMonth {
    width: 100%;
    margin-bottom: 30px;
  }
  .rdrDateRangePickerWrapper.PreviewArea {
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .ant-menu-inline-collapsed-tooltip {
    display: none;
  }
}

@media only screen and (max-width: 800px) {
  .ant-page-header-heading-left {
    max-width: 320px;
  }
}

@media only screen and (max-width: 767px) {
  .ant-page-header {
    padding: 26px 15px 26px 15px !important;
  }
  .ant-page-header-heading {
    flex-flow: column;
    align-items: center;
    justify-content: center !important;
  }
  .ant-page-header-heading-left {
    max-width: 100%;
  }
  .ant-page-header-heading .ant-page-header-heading-title {
    margin-right: 0;
    white-space: normal;
    text-align: center;
  }
  .ant-page-header-heading-extra {
    white-space: normal !important;
    margin: 12px 0 4px !important;
  }
  .ant-page-header-heading-extra .page-header-actions {
    white-space: normal !important;
    text-align: center;
  }
  .ant-card-body {
    padding: 20px !important;
  }
  .ant-card-head {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }
  /* .ant-pagination .ant-pagination-item,
  .ant-pagination .ant-pagination-item.ant-pagination-prev,
  .ant-pagination .ant-pagination-item.ant-pagination-next,
  .ant-pagination .ant-pagination-jump-prev {
    margin-right: 5px;
  }
  .ant-pagination li.ant-pagination-item,
  .ant-pagination li.ant-pagination-prev,
  .ant-pagination li.ant-pagination-next {
    height: 25px;
    min-width: 25px;
    line-height: 22px;
  }
  .ant-pagination li.ant-pagination-prev .anticon,
  .ant-pagination li.ant-pagination-next .anticon {
    vertical-align: 0.15em;
  } */
  .ant-table-pagination {
    float: none !important;
    text-align: center;
  }
  .ant-table-pagination li.ant-pagination-total-text {
    display: block;
    margin-bottom: 8px;
  }
  .ant-table-pagination li {
    margin-right: 8px !important;
  } /*
  .ant-pagination .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    height: 25px !important;
  } */
  /* .ant-pagination .ant-select-single .ant-select-selector .ant-select-selection-item {
    line-height: 23px !important;
  } */
  .rdrDefinedRangesWrapper {
    border-right: 0 none;
  }
}

@media only screen and (max-width: 575px) {
  .btn-inc,
  .btn-dec {
    height: 30px;
    width: 30px;
  }
  .ant-page-header {
    padding: 16px 20px !important;
  }
  .ant-layout-header {
    height: auto;
  }
  .ant-card-head {
    line-height: 1;
  }
  .ant-card-head-title {
    white-space: normal !important;
  }
  .pop-confirm .ant-btn {
    max-width: 60px;
    padding: 0 20px;
  }
  .pop-confirm.pop-confirm-right {
    margin-left: 320px !important;
  }
  .pop-confirm.pop-confirm-bottom {
    margin-left: 95px !important;
  }
  /* Card Heading */
  .ant-card-head-wrapper {
    flex-flow: column;
    align-items: center;
  }
  .ant-card-head-wrapper .ant-card-extra {
    padding: 10px 0 16px !important;
    float: none;
    margin: 0;
    flex-flow: column;
  }
  .ant-card-head-wrapper .ant-card-extra .ant-dropdown-trigger + .card-nav {
    margin-bottom: 8px;
  }
  .ant-card-head-wrapper .ant-card-extra .ant-dropdown-trigger {
    margin: 0 !important;
  }
  /* .ant-card-head-wrapper .ant-card-head-title {
    padding: 20px 0 0px !important;
  } */
  .card-nav ul li.active:before {
    display: none;
  }
  .ant-card-head-title > div {
    display: flex;
    flex-flow: column;
    align-items: center;
  }
  .ant-card-head-title > div img {
    margin-right: 0;
    margin-bottom: 10px;
  }
  .ant-card-head-title > div span {
    margin: 8px 0 0;
  }
  .ninjadash-top-dropdown {
    width: 100%;
  }
  .ninjadash-top-dropdown a.btn-seeAll {
    width: 100%;
    left: 0;
    right: 0;
    height: auto;
    bottom: 0;
    text-align: center;
    justify-content: center;
  }
  .drawer-default .ant-drawer-content-wrapper {
    width: 300px !important;
  }
  .ant-steps .ant-steps-item-tail {
    display: none !important;
  }
  .testimonial-block .swiper {
    display: flex;
    flex-flow: column;
  }
  .testimonial-block .swiper .swiper-pagination {
    order: 5;
    margin-top: 30px;
    position: static;
  }
}

@media only screen and (max-width: 480px) {
  .ant-popover {
    width: 100%;
  }
}

.bmzxig .ant-table tr th:first-child,
.bmzxig .ant-table tr td:first-child {
  padding-right: 15px;
}

/* Emprt */

.ant-empty .ant-empty-footer button {
  padding: 0px 15.58px !important;
}

/* Add Event Modal */
.addEvent-modal .ant-modal-header {
  padding: 20px 25px;
}
.addEvent-modal .ant-modal-header .ant-modal-close-x svg {
  fill: #5a5f7d;
}
.addEvent-modal .ant-modal-header .ant-modal-title {
  font-size: 15px;
  font-weight: 500;
  color: #272b41;
}

.addEvent-modal .ant-modal-body {
  padding: 23px 25px;
}

/* Event Dropdown */

.event-dropdown {
  min-width: auto !important;
  max-width: 450px;
  padding: 0 !important;
  margin: 6px 0 0 !important;
  box-shadow: 0 10px 40px #9299b820 !important;
}

.event-dropdown div {
  border-radius: 8px;
}

.event-dropdown .ant-card {
  width: 100% !important;
  margin-bottom: 0 !important;
}

/* Tag Filter Modal - 可調整大小的選擇對話框 */
.tag-filter-modal .ant-modal-content {
  resize: both;
  overflow: auto;
  min-width: 600px;
  min-height: 400px;
  max-width: 90vw;
  max-height: 90vh;
}

.tag-filter-modal .ant-modal-body {
  overflow: auto;
  max-height: calc(90vh - 120px);
}

.tag-filter-modal .ant-modal-content::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background: linear-gradient(-45deg, transparent 0%, transparent 40%, #ccc 40%, #ccc 60%, transparent 60%);
  cursor: nw-resize;
  pointer-events: none;
}

.tag-filter-modal .section-title {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tag-filter-modal .clear-all-btn {
  font-size: 12px;
  color: #ff4d4f;
  padding: 0 4px;
  height: auto;
  line-height: 1;
}

.tag-filter-modal .clear-all-btn:hover {
  color: #ff7875;
}

/* 拖曳功能 */
.tag-filter-modal .ant-modal-header,
.tag-filter-modal .modal-header,
.tag-filter-modal .modal-drag-handle {
  cursor: move !important;
  user-select: none;
}

.tag-filter-modal .ant-modal-header:hover,
.tag-filter-modal .modal-header:hover,
.tag-filter-modal .modal-drag-handle:hover {
  background-color: #f0f0f0;
}

.tag-filter-modal.dragging {
  pointer-events: none;
}

.tag-filter-modal.dragging .ant-modal-content,
.tag-filter-modal.dragging .modal-content {
  pointer-events: auto;
}

/* 確保拖曳時的視覺反饋 */
.tag-filter-modal.dragging .ant-modal-header,
.tag-filter-modal.dragging .modal-header,
.tag-filter-modal.dragging .modal-drag-handle {
  background-color: #e6f7ff;
}

/* Range Calendar Style */
.ninjadash-range-calendar {
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(143, 142, 159, 0.2);
}
.ninjadash-range-calendar .vc-pane-container .vc-arrows-container {
  top: -6px;
}
.ninjadash-range-calendar .vc-pane-container .vc-pane + .vc-pane {
  padding-left: 10px;
}
.ninjadash-range-calendar .vc-arrows-container .vc-arrow svg {
  width: 18px;
  height: 18px;
  fill: #272b41;
}
.ninjadash-range-calendar .vc-arrows-container .vc-arrow {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}
.ninjadash-range-calendar .vc-arrows-container .vc-arrow:hover {
  background-color: #f7f7fe;
}
.ninjadash-range-calendar .vc-arrows-container .vc-arrow:hover svg {
  fill: #ff8000;
}
.ninjadash-range-calendar .vc-container {
  border: 0 none;
}
.ninjadash-range-calendar .vc-pane .vc-header {
  padding: 0;
}
.ninjadash-range-calendar .vc-pane .vc-header .vc-title {
  font-size: 14px;
  font-family: arial;
  color: #272b41;
  font-weight: 500;
  padding: 4px 0;
}
.ninjadash-range-calendar .vc-pane .vc-weeks .vc-weekday {
  font-size: 12px;
  font-weight: 500;
  font-family: arial;
  color: #666d92;
  padding-bottom: 0;
}
.ninjadash-range-calendar .vc-pane .vc-weeks .vc-day {
  padding: 2px 0;
}
.ninjadash-range-calendar .vc-pane .vc-weeks .vc-day .vc-day-content {
  font-family: arial;
  font-size: 13px;
  font-weight: 500;
  color: #272b41;
  width: 36px;
  height: 36px;
  line-height: 40px;
}
.ninjadash-range-calendar .vc-pane .vc-weeks .vc-day .vc-day-content:hover {
  background-color: #eee;
}
.ninjadash-range-calendar .vc-pane .vc-weeks .vc-day.is-today .vc-day-content {
  background-color: #ff8000;
  color: #fff;
}

/* SVG map Style */
.svgMap-tooltip .svgMap-tooltip-content-container {
  padding: 10px;
  min-width: 130px;
}
.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-content {
  display: none;
}
.svgMap-tooltip
  .svgMap-tooltip-content-container
  .svgMap-tooltip-flag-container.svgMap-tooltip-flag-container-emoji {
  font-size: 22px !important;
  line-height: 0 !important;
  padding: 4px 0 6px !important;
}
.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-title {
  font-size: 13px;
  line-height: 20px;
  padding-bottom: 0;
}

@media only screen and (max-width: 479px) {
  .placement-confirm {
    display: flex;
    flex-wrap: wrap;
    margin: -5px -10px 0 -5px;
  }
  .placement-confirm .pop-confirm {
    flex: 0 0 50%;
  }
  .pop-confirm .ant-btn {
    display: block;
    padding: 0 20px;
    max-width: 80px;
    min-width: 80px;
    margin: 10px;
  }
  .pop-confirm.pop-confirm-right {
    margin-left: 0px !important;
  }
  .pop-confirm.pop-confirm-top {
    margin-left: 0px !important;
  }
  .pop-confirm.pop-confirm-bottom {
    margin-left: 0px !important;
    margin-top: 0px !important;
  }
}

@media only screen and (max-width: 400px) {
  .ant-select {
    width: 100% !important;
  }
  .rdrDefinedRangesWrapper {
    width: 100% !important;
  }
  .rdrDateRangePickerWrapper {
    flex-wrap: wrap;
  }
  .ninjadash-top-dropdwon {
    width: 280px;
    min-width: 180px;
  }
  .ninjadash-top-dropdwon .ninjadash-top-dropdwon__title {
    min-height: 40px;
  }
  .ninjadash-top-dropdwon .ninjadash-top-dropdwon__nav li:not(:last-child) {
    margin-bottom: 10px;
  }
  .ninjadash-top-dropdwon .ninjadash-top-dropdwon__nav li a {
    padding: 10px 0px;
  }
  .ninjadash-top-dropdwon .ninjadash-top-dropdwon__content img {
    margin-right: 15px;
  }
  .ninjadash-top-dropdwon .ninjadash-top-dropdwon__content figcaption {
    margin-left: 0;
  }
  .ninjadash-top-dropdwon
    .ninjadash-top-dropdwon__content
    figcaption
    .ninjadash-top-dropdwonText {
    min-width: 155px;
  }
  .ant-drawer-content-wrapper {
    width: 260px !important;
  }
}

@media only screen and (max-width: 379px) {
  .ant-card-head-wrapper .ant-card-extra .ant-radio-button-wrapper {
    height: 32px !important;
    line-height: 30px !important;
  }
  .ant-notification-notice {
    width: 275px;
  }
}

@media (max-width: 991px) {
  .mail-sideabr {
    box-shadow: 0 0 10px #00000020;
  }
}

/* .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn{
  color: #fff !important;
}
.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn svg{
	fill: #fff;
} */

/* custom label */

/* card spin loader */

.ant-card-body .sd-spin div,
.ant-card-body .spin div {
  position: relative;
}

.ant-card-body .sd-spin,
.ant-card-body .spin {
  height: 200px;
}

.ant-card-body {
  position: relative;
}

.ant-card-body .sd-spin .ant-spin,
.ant-card-body .spin .ant-spin {
  display: flex;
  align-items: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  min-height: 200px;
}

.starActive svg {
  fill: rgb(255, 157, 0) !important;
}

/* vectormap zoom in / zoom out */

.jvectormap-zoomin,
.jvectormap-zoomout {
  width: 27px;
  height: 27px;
  background: none;
  color: #5a5f7d;
  border: 1px solid #f1f2f6;
  padding: 0;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  left: auto;
  right: 0;
  top: auto;
  background-color: #fff;
}

.jvectormap-zoomin {
  border-radius: 6px 6px 0 0;
  bottom: 36px;
}

.jvectormap-zoomout {
  border-radius: 0 0 6px 6px;
  bottom: 10px;
}

.jvectormap-tip {
  padding: 7px 12px;
  border: 0 none;
  font-size: 12px;
  background: #272b41;
}

.btn-rtl {
  width: 60px;
  height: 60px;
  border: 1px solid #fff;
  background: #4347d9;
  color: #fff;
  position: fixed;
  right: 0;
  top: 50%;
  margin-top: -30px;
  z-index: 99999999;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Auto Complate */
.auto-complete-input .ant-select-single .ant-select-selector input {
  padding: 7px 12px !important;
}

/* masonry-grid start from here */

.my-masonry-grid {
  display: -webkit-box;
  /* Not needed if autoprefixing */
  display: -ms-flexbox;
  /* Not needed if autoprefixing */
  display: flex;
  margin-left: -10px;
  /* gutter size offset */
  width: auto;
}

.my-masonry-grid_column {
  padding-left: 10px;
  /* gutter size */
  background-clip: padding-box;
}

/* Style your items */

.my-masonry-grid_column > div {
  /* change div to reference your elements you put in <Masonry> */
  background: grey;
  margin-bottom: 30px;
}

.common-ul {
  padding: 5px 0;
  padding-left: 10px;
}

.display {
  display: none;
}

li.active > .display {
  display: block;
}

/* rtl Style from here */

html[dir="rtl"]
  .ant-form-item-label
  > label.ant-form-item-required:not(
    .ant-form-item-required-mark-optional
  )::before {
  margin-left: 4px;
}

html[dir="rtl"] .table-bordered .ant-table-thead tr th:last-child,
html[dir="rtl"] .table-bordered .ant-table-tbody tr td:last-child {
  text-align: left;
}
html[dir="rtl"] .ant-table table {
  text-align: right;
}
html[dir="rtl"] .ant-page-header .ant-page-header-heading-title {
  padding-right: 0;
  padding-left: 12px;
}
html[dir="rtl"] .ant-card-extra .ant-dropdown-trigger {
  margin-left: 0;
}
html[dir="rtl"] .ant-card-head-title span {
  /* margin-right: 15px; */
}
html[dir="rtl"] span[aria-label="arrow-right"] svg,
html[dir="rtl"] span[aria-label="arrow-left"] svg {
  transform: rotateY(180deg);
}
html[dir="rtl"] .ant-menu-vertical .ant-menu-submenu-title {
  padding-right: 15px !important;
}
html[dir="rtl"] .ant-modal-close {
  right: auto;
  left: 0;
}
html[dir="rtl"] .ant-result-extra > * {
  margin-right: 0;
  margin-left: 8px;
}

/* Menu RTL Style */
html[dir="rtl"] .ant-menu.ant-menu-vertical {
  text-align: right !important;
}
html[dir="rtl"]
  .ant-menu.ant-menu-vertical
  .ant-menu-submenu-placement-rightTop {
  left: -170px !important;
}
html[dir="rtl"] .ant-menu.ant-menu-vertical .ant-menu-vertical-left {
  text-align: right;
}
html[dir="rtl"] .ant-menu.ant-menu-vertical .ant-menu-submenu-arrow {
  right: auto;
  left: 16px;
  transform: rotateY(180deg);
}
html[dir="rtl"] .ant-tag .anticon-close {
  margin-left: 0;
  margin-right: 3px;
}
html[dir="rtl"] .ant-slider-vertical .ant-slider-handle {
  margin-left: 0;
  margin-right: -5px;
}

/* Ui Elements */
html[dir="rtl"] .ant-select-arrow {
  right: auto;
  left: 11px;
}
html[dir="rtl"] .ant-cascader-picker-arrow {
  right: auto;
  left: 12px;
}
html[dir="rtl"] .ant-steps-item-title::after {
  left: auto;
  right: 100%;
}
html[dir="rtl"] .ant-steps-small .ant-steps-item-icon {
  margin-right: 0;
  margin-left: 8px;
}
html[dir="rtl"] .ant-rate-text {
  margin-right: 8px;
  margin-left: 0;
}
html[dir="rtl"] .ant-steps-small .ant-steps-item-title {
  padding-right: 0;
  padding-left: 12px;
}
html[dir="rtl"]
  .ant-steps-horizontal:not(.ant-steps-label-vertical)
  .ant-steps-item {
  margin-right: 0;
  margin-left: 16px;
}

html[dir="rtl"] .ant-steps-item-icon {
  margin-right: 0;
  margin-left: 8px;
}
html[dir="rtl"] .ant-steps-item-title {
  padding-right: 0;
  padding-left: 16px;
}

html[dir="rtl"] .ant-statistic-content-prefix {
  margin-right: 0;
  margin-left: 4px;
}
html[dir="rtl"]
  .ant-steps-vertical.ant-steps-small
  .ant-steps-item-container
  .ant-steps-item-tail {
  left: auto;
  right: 12px;
}

html[dir="rtl"] .ant-steps-vertical .ant-steps-item-icon {
  float: right;
}

html[dir="rtl"] .ant-radio-button-wrapper:first-child {
  border-radius: 0 4px 4px 0;
}
html[dir="rtl"] .ant-radio-button-wrapper:last-child {
  border-radius: 4px 0 0 4px;
}

html[dir="rtl"] .ant-input-affix-wrapper .ant-input-prefix {
  margin-right: 0;
  margin-left: 4px;
}

html[dir="rtl"] .ant-message .anticon {
  margin-right: 0;
  margin-left: 8px;
}

html[dir="rtl"] .ant-upload-list-item-name {
  padding-right: 22px;
  padding-left: 0;
}
html[dir="rtl"] .ant-timeline-item-tail {
  left: auto;
  right: 4px;
  border-right: 0 none;
  border-left: 2px solid #f0f0f0;
}
html[dir="rtl"] .ant-timeline-item-head-custom {
  left: auto;
  right: 5px;
  transform: translate(50%, -50%);
}
html[dir="rtl"] .ant-timeline.ant-timeline-alternate .ant-timeline-item-tail,
html[dir="rtl"] .ant-timeline.ant-timeline-alternate .ant-timeline-item-head,
html[dir="rtl"]
  .ant-timeline.ant-timeline-alternate
  .ant-timeline-item-head-custom,
html[dir="rtl"]
  .ant-timeline.ant-timeline-right
  .ant-timeline-item-head-custom {
  left: auto;
  right: 50%;
}
html[dir="rtl"]
  .ant-timeline-rtl.ant-timeline.ant-timeline-alternate
  .ant-timeline-item-head-custom,
html[dir="rtl"]
  .ant-timeline-rtl.ant-timeline.ant-timeline-label
  .ant-timeline-item-head-custom,
html[dir="rtl"]
  .ant-timeline-rtl.ant-timeline.ant-timeline-right
  .ant-timeline-item-head-custom {
  margin-right: 1px;
  margin-left: 0;
}
html[dir="rtl"]
  .ant-timeline.ant-timeline-alternate
  .ant-timeline-item-left
  .ant-timeline-item-content,
html[dir="rtl"]
  .ant-timeline.ant-timeline-right
  .ant-timeline-item-left
  .ant-timeline-item-content {
  left: auto;
  right: calc(50% - 4px);
  text-align: right;
}
html[dir="rtl"] .ant-timeline-item-content {
  margin: 0 18px 0 0;
}

html[dir="rtl"] .ant-upload-list-item-card-actions {
  right: auto;
  left: 0;
}
html[dir="rtl"] .ant-upload-list-item-card-actions .anticon {
  padding-left: 6px;
  padding-right: 0px;
}
html[dir="rtl"]
  .ant-upload-list-item-list-type-text:hover
  .ant-upload-list-item-name-icon-count-1 {
  padding-right: 22px;
  padding-left: 0;
}
html[dir="rtl"] .ant-upload-list-item-info {
  padding: 0 4px 0 12px;
}

html[dir="rtl"] .ant-time-picker-icon,
html[dir="rtl"] .ant-time-picker-clear {
  right: auto;
  left: 11px;
}

html[dir="rtl"] .ant-notification-notice-close {
  right: auto;
  left: 22px;
}

html[dir="rtl"]
  .ant-notification-notice-closable
  .ant-notification-notice-message {
  padding-right: 0;
  padding-left: 22px;
}

html[dir="rtl"]
  .ant-notification-notice-with-icon
  .ant-notification-notice-message {
  margin-left: 0;
  margin-right: 48px;
}

html[dir="rtl"] .ant-menu-item .anticon,
html[dir="rtl"] .ant-menu-submenu-title .anticon {
  margin-right: 0;
  margin-left: 10px;
}

html[dir="rtl"] .ant-menu-inline,
html[dir="rtl"] .ant-menu-vertical,
html[dir="rtl"] .ant-menu-vertical-left {
  border-right: 0 none;
  border-left: 1px solid #f0f0f0;
}

html[dir="rtl"] .ant-calendar-picker-clear,
html[dir="rtl"] .ant-calendar-picker-icon {
  right: auto;
  left: 12px;
}

html[dir="rtl"] .ant-comment-avatar {
  margin-right: 0;
  margin-left: 12px;
}

.tox .tox-toolbar,
.tox .tox-toolbar__overflow,
.tox .tox-toolbar__primary {
  background: none !important;
  border-bottom: 1px solid #f1f2f6;
}

.tox .tox-statusbar {
  border-top: 1px solid #f1f2f6 !important;
}

/* custom scroll */
.ps__rail-y .ps__thumb-y {
  background-color: rgb(241, 242, 246);
}
.ps__rail-y:hover > .ps__thumb-y,
.ps__rail-y:focus > .ps__thumb-y,
.ps__rail-y.ps--clicking .ps__thumb-y {
  background-color: #c9cddc !important;
}

/* unicon div line-height reset */
.unicon {
  line-height: 0;
}

/* chart canvas margin reset */
.ninjadash-chart-container canvas {
  margin-bottom: 0 !important;
}

.dark-mode .ant-modal-content {
  background: #282b37;
}

.no-data-text {
  padding: 0 25px;
}

/* Swiper slider */
.swiper-horizontal.swiper-pointer-events {
  padding: 60px 0;
}

/* Auth0 style */

.auth0-lock.auth0-lock .auth0-lock-header-logo {
  height: 32px !important;
  margin: 0 0 3px !important;
}

.auth0-lock.auth0-lock .auth0-lock-name {
  font-size: 16px !important;
}

/* Dark Mode Css */

.dark-mode .ninjadash-top-dropdown a.btn-seeAll,
.dark-mode .ninjadash-top-dropdown .ninjadash-top-dropdown__nav li a:hover,
.dark-mode .ant-picker-panel-container,
.dark-mode .editor-compose .ant-input,
.dark-mode .ant-drawer-content,
.dark-mode .ant-dropdown,
.dark-mode .mail-props,
.dark-mode .ant-popover-inner,
.dark-mode .ninjadash-searchbar .ant-input,
.dark-mode .ant-input-group-addon,
.dark-mode .ant-popover-arrow-content,
.dark-mode .ant-drawer-header,
.dark-mode .ant-popover-arrow-content:before {
  background: #1b1e2b;
}
.dark-mode .ant-btn.ant-btn-white,
.dark-mode .ant-notification-notice,
.dark-mode .ant-pagination-prev .ant-pagination-item-link,
.dark-mode .ant-pagination-next .ant-pagination-item-link,
.dark-mode .ant-pagination .ant-pagination-prev,
.dark-mode .ant-pagination .ant-pagination-next,
.dark-mode .ant-pagination .ant-pagination-jump-prev,
.dark-mode .ant-pagination .ant-pagination-jump-next,
.dark-mode .ant-pagination .ant-pagination-item,
.dark-mode .ant-pagination .ant-pagination-options .ant-select-selector,
.dark-mode .ant-modal .ant-modal-header,
.dark-mode .ant-modal .ant-modal-content,
.dark-mode .ant-modal .ant-modal-content .ant-card,
.dark-mode .ant-input,
.dark-mode .ant-select:not(.ant-select-customize-input) .ant-select-selector,
.dark-mode .ant-form-item-control-input .ant-picker,
.dark-mode .ant-pagination-options-quick-jumper input,
.dark-mode .ant-select-dropdown {
  background-color: #282b37;
}
.dark-mode
  .ninjadash-task-details
  .ninjadash-task-details-modal
  .ninjadash-task-details-modal__description
  textarea,
.dark-mode .ant-picker-range-arrow::before,
.dark-mode .ninjadash-top-dropdown .ninjadash-top-dropdown__title {
  background-color: #323541;
}

.dark-mode .ant-picker-range-arrow {
  background: linear-gradient(135deg, transparent 40%, #323541 40%);
}

.dark-mode .ant-btn.ant-btn-white,
.dark-mode .ant-btn.ant-btn-dark,
.dark-mode .ant-btn.ant-btn-dashed,
.dark-mode .RichTextEditor__root___2QXK- .ButtonWrap__root___1EO_R button,
.dark-mode .ant-popover-inner .ant-popover-inner-content a,
.dark-mode .pop-confirm .ant-btn,
.dark-mode .ant-pagination-disabled .ant-pagination-item-link,
.dark-mode .ant-pagination-disabled:hover .ant-pagination-item-link,
.dark-mode .ant-pagination .ant-pagination-item a,
.dark-mode .ant-notification-notice-description,
.dark-mode .ant-notification-notice-close-x svg,
.dark-mode .ant-modal-close-x svg,
.dark-mode .ant-drawer-body,
.dark-mode .ant-modal-body,
.dark-mode .ant-modal-confirm-body .ant-modal-confirm-content,
.dark-mode .ant-form-item-control-input .ant-picker,
.dark-mode .ant-select-arrow svg,
.dark-mode .ant-picker-range-separator svg,
.dark-mode .ant-picker-suffix svg,
.dark-mode .ninjadash-searchbar .ant-input,
.dark-mode .ant-picker-cell.ant-picker-cell-in-view,
.dark-mode .ninjadash-task-details .ant-modal-header .ant-modal-title .sub-text,
.dark-mode .ant-popover-message-title {
  color: #a4a5aa;
}

.dark-mode .ant-picker-cell {
  color: #a4a5aa25;
}

.dark-mode .ninjadash-top-dropdown__title,
.ninjadash-task-details
  .ant-modal-body
  .ninjadash-checklist-row
  .ninjadash-checklist-item__title,
.dark-mode
  .ninjadash-top-dropdown
  .ninjadash-top-dropdown__content
  figcaption
  h1,
.dark-mode
  .ninjadash-top-dropdown
  .ninjadash-top-dropdown__nav
  li
  a:hover
  figcaption
  h1,
.dark-mode .ninjadash-task-details .ant-modal-header .ant-modal-title h4,
.dark-mode .ant-modal-title,
.dark-mode .ant-modal-confirm-body .ant-modal-confirm-title,
.dark-mode .ant-drawer-title,
.dark-mode .ant-form-item-label > label,
.dark-mode .ant-picker-header button,
.dark-mode .ant-notification-notice-message {
  color: #fff;
}
.dark-mode .ant-btn.ant-btn-white:hover {
  color: #ff8000;
}
.dark-mode
  .ninjadash-task-details
  .ant-modal-body
  .ninjadash-checklist-tasks
  .ant-checkbox-wrapper
  .ant-checkbox-inner,
.dark-mode .ant-btn.ant-btn-dark,
.dark-mode .ant-btn.ant-btn-white.btn-outlined,
.dark-mode .pop-confirm .ant-btn,
.dark-mode .ant-pagination .ant-pagination-prev,
.dark-mode .ant-pagination .ant-pagination-next,
.dark-mode .ant-pagination .ant-pagination-jump-prev,
.dark-mode .ant-pagination .ant-pagination-jump-next,
.dark-mode .ant-pagination .ant-pagination-item,
.dark-mode .ant-pagination-options-quick-jumper input,
.dark-mode .ant-modal-header,
.dark-mode .ant-modal-footer,
.dark-mode .ant-pagination .ant-pagination-options .ant-select-selector,
.dark-mode .ant-input-group-addon,
.dark-mode .ant-select:not(.ant-select-customize-input) .ant-select-selector,
.dark-mode .ant-form-item-control-input .ant-picker,
.dark-mode .ant-picker-header,
.dark-mode .ant-picker-panel-container .ant-picker-panel,
.dark-mode .ant-drawer-header,
.dark-mode .ant-input {
  border-color: #323541 !important;
}
.dark-mode .ant-btn.ant-raised.ant-btn-white,
.dark-mode .ant-badge-dot,
.dark-mode .ant-badge-count {
  box-shadow: 0 5px 10px #8c90a410;
}

.dark-mode .ant-btn.ant-btn-dashed {
  border-color: #494b55;
}

.dark-mode .ninjadash-navbar-menu {
  margin-left: 20px;
}

.dark-mode .ant-tooltip .ant-tooltip-inner {
  color: #a4a5aa;
  background-color: #282b37;
  border: 1px solid #323541;
  box-shadow: 0 8px 15px #8c90a410;
}

.dark-mode .ant-tooltip .ant-tooltip-arrow .ant-tooltip-arrow-content {
  background-color: #282b37;
  border: 1px solid #323541;
  z-index: 1;
}

.dark-mode .ant-slider-handle {
  background-color: #282b37;
}

/* Breadcrumb links wrapper */
.ant-page-header-content {
  padding-top: 0 !important;
}

/* 修復圖表文字顏色 - 強制設置為黑色 */
.apexcharts-text,
.apexcharts-xaxis-label,
.apexcharts-yaxis-label,
.apexcharts-legend-text,
.apexcharts-datalabel,
.apexcharts-tooltip-text,
.apexcharts-xaxis-title-text,
.apexcharts-yaxis-title-text,
.apexcharts-gridline,
.apexcharts-xaxis-tick,
.apexcharts-yaxis-tick {
  fill: #000000 !important;
  color: #000000 !important;
}

/* 修復 ApexCharts 軸標籤 */
.apexcharts-xaxis text,
.apexcharts-yaxis text {
  fill: #000000 !important;
}

/* 修復 ApexCharts 圖例 */
.apexcharts-legend text {
  fill: #000000 !important;
  color: #000000 !important;
}

/* 修復 ApexCharts 數據標籤 */
.apexcharts-datalabels text {
  fill: #000000 !important;
}

/* 修復 Chart.js 文字顏色 */
.chartjs-render-monitor {
  color: #000000 !important;
}

/* 修復所有圖表容器內的文字 */
.chart-container,
.ninjadash-chart-container,
.apexcharts-canvas {
  color: #000000 !important;
}

.chart-container *,
.ninjadash-chart-container *,
.apexcharts-canvas * {
  color: #000000 !important;
}
