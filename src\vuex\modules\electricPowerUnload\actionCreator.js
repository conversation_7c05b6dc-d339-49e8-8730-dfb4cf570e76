import mutations from "./mutations";
import { DataService } from "@/config/dataService/dataService";
import { Modal } from "ant-design-vue";

const state = {
  // 基礎資料
  summaryDetailList: [],  // 摘要詳細列表
  modeCodeNamePairs: [],  // 模式選項
  alarmTagList: [],       // 警報測點列表
  consumableTagList: [],  // 可消耗測點列表
  tagList: [],           // 所有測點列表
  
  // 系統設定
  config: {
    contractCapacity: 250,
    stage1: true,
    stage2: true,
    stage3: false,
    loadUnloadTagOnlyDigital: true,
    loadUnloadTagMustReadAndWrite: true,
    enableLoad: false
  },
  
  // 即時監控資料
  monitorData: {
    currentDemand: 0,
    unloadStage: 0,
    isUnloading: false
  },
  
  // UI 狀態
  loading: false,
  error: null
};

const actions = {
  // 取得電力卸載階段群組詳細列表
  async getStageGroupDetailList({ commit }) {
    try {
      commit("getDataBegin");
      
      const response = await DataService.get("/api/ElectricPowerUnload/GetElectricPowerUnloadStageGroupDetailList");
      
      if (response.data.ReturnCode === 1) {
        commit("getStageGroupDetailListSuccess", response.data.Detail);
      } else {
        throw new Error(response.data.Message || "載入失敗");
      }
    } catch (err) {
      commit("getDataErr", err);
      Modal.error({
        title: "載入失敗",
        content: err.message || "無法載入電力卸載資料"
      });
    }
  },

  // 取得測點列表
  async getTagList({ commit }) {
    try {
      const response = await DataService.get("/api/ElectricPowerUnload/GetElectricPowerConsumableTagForUnloadList");
      
      if (response.data.ReturnCode === 1) {
        commit("getTagListSuccess", response.data.Detail.ElectricPowerConsumableTagList || []);
      } else {
        throw new Error(response.data.Message || "載入測點失敗");
      }
    } catch (err) {
      console.error("載入測點失敗:", err);
    }
  },

  // 取得警報測點列表（使用所有測點）
  async getAlarmTagList({ commit }) {
    try {
      const response = await DataService.get("/api/Tag/GetTagList");
      
      if (response.data.ReturnCode === 1) {
        // 將所有測點作為警報測點選項
        const allTags = response.data.Detail.TagList || [];
        commit("getAlarmTagListSuccess", allTags);
      } else {
        throw new Error(response.data.Message || "載入警報測點失敗");
      }
    } catch (err) {
      console.error("載入警報測點失敗:", err);
    }
  },

  // 建立電力卸載摘要
  async createSummary({ commit, dispatch }, data) {
    try {
      commit("getDataBegin");
      
      const formData = new FormData();
      formData.append("Name", data.summaryName);
      formData.append("Mode", data.mode?.toString() || "0");
      formData.append("ContinuedSecond", (data.continuedSecond || 0).toString());
      formData.append("IsLoad", data.isLoad?.toString() || "false");
      
      // 使用正確的 Content-Type 處理 FormData
      const response = await DataService.post("/api/ElectricPowerUnload/CreateElectricPowerUnloadSummary", formData);
      
      if (response.data.ReturnCode === 1) {
        // 先刷新摘要列表
        await dispatch("getStageGroupDetailList");
        
        // 如果有選擇可消耗測點，需要設置測點
        if (data.consumableTagIdList && data.consumableTagIdList.length > 0) {
          // 從最新的摘要列表中找到剛創建的摘要
          const state = this.state.electricPowerUnload;
          const latestSummary = state.summaryDetailList.find(summary => 
            summary.SummaryName === data.summaryName && 
            summary.Mode === data.mode &&
            summary.ContinueSeconds === data.continuedSecond &&
            summary.IsLoad === data.isLoad
          );
          
          if (latestSummary && latestSummary.SummaryId) {
            const tagFormData = new FormData();
            tagFormData.append("SummaryId", latestSummary.SummaryId);
            
            // 添加所有測點 ID
            data.consumableTagIdList.forEach(tagId => {
              if (tagId) {
                tagFormData.append("ElectricPowerConsumableTagIdList", tagId);
              }
            });
            
            const tagResponse = await DataService.post("/api/ElectricPowerUnload/EditElectricPowerConsumableTagForUnload", tagFormData);
            
            if (tagResponse.data.ReturnCode !== 1) {
              throw new Error(tagResponse.data.Message || "設置可消耗測點失敗");
            }
            
            // 再次刷新列表以顯示測點信息
            await dispatch("getStageGroupDetailList");
          }
        }
        
        return response.data;
      } else {
        throw new Error(response.data.Message || "建立失敗");
      }
    } catch (err) {
      commit("getDataErr", err);
      throw err;
    }
  },

  // 編輯電力卸載摘要
  async editSummary({ commit, dispatch }, data) {
    try {
      commit("getDataBegin");
      
      // 1. 更新摘要基本信息
      const formData = new FormData();
      formData.append("Id", data.summaryId);  // 後端期望 Id 而不是 SummaryId
      formData.append("Name", data.summaryName);
      formData.append("Mode", data.mode?.toString() || "0");
      formData.append("ContinuedSecond", (data.continuedSecond || 0).toString());
      formData.append("IsLoad", data.isLoad?.toString() || "false");
      
      const response = await DataService.post("/api/ElectricPowerUnload/EditElectricPowerUnloadSummary", formData);
      
      if (response.data.ReturnCode === 1) {
        // 2. 更新可消耗測點
        if (data.consumableTagIdList && data.consumableTagIdList.length > 0) {
          const tagFormData = new FormData();
          tagFormData.append("SummaryId", data.summaryId);
          
          // 添加所有測點 ID
          data.consumableTagIdList.forEach(tagId => {
            if (tagId) {
              tagFormData.append("ElectricPowerConsumableTagIdList", tagId);
            }
          });
          
          const tagResponse = await DataService.post("/api/ElectricPowerUnload/EditElectricPowerConsumableTagForUnload", tagFormData);
          
          if (tagResponse.data.ReturnCode !== 1) {
            throw new Error(tagResponse.data.Message || "更新可消耗測點失敗");
          }
        }
        
        await dispatch("getStageGroupDetailList");
        return response.data;
      } else {
        throw new Error(response.data.Message || "更新失敗");
      }
    } catch (err) {
      commit("getDataErr", err);
      throw err;
    }
  },

  // 刪除電力卸載摘要
  async deleteSummary({ commit, dispatch }, summaryId) {
    try {
      commit("getDataBegin");
      
      const formData = new FormData();
      formData.append("SummaryId", summaryId);
      
      const response = await DataService.post("/api/ElectricPowerUnload/DeleteElectricPowerUnloadSummary", formData);
      
      if (response.data.ReturnCode === 1) {
        await dispatch("getStageGroupDetailList");
        return response.data;
      } else {
        throw new Error(response.data.Message || "刪除失敗");
      }
    } catch (err) {
      commit("getDataErr", err);
      throw err;
    }
  },

  // 建立電力卸載階段群組
  async createStageGroup({ commit, dispatch }, data) {
    try {
      commit("getDataBegin");

      // 調試信息
      console.log('createStageGroup 接收到的資料:', data);

      const formData = new FormData();
      formData.append("SummaryId", data.summaryId || "");
      formData.append("StageCode", data.stageCode?.toString() || "0");
      formData.append("StageName", data.stageName || "");
      formData.append("UnloadULmt", data.unloadULmt?.toString() || "0");
      formData.append("LoadLLmt", data.loadLLmt?.toString() || "0");
      formData.append("ModifyMode", 1); // 1 = 新增 (數字類型)

      // 只有在有值時才添加警報測點 ID
      if (data.unloadAlarmTagId) {
        formData.append("UnloadAlarmTagId", data.unloadAlarmTagId);
      }
      if (data.loadAlarmTagId) {
        formData.append("LoadAlarmTagId", data.loadAlarmTagId);
      }

      // 調試 FormData 內容
      console.log('FormData 內容:');
      for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
      }

      const response = await DataService.post("/api/ElectricPowerUnload/EditElectricPowerUnloadStageGroup", formData);

      if (response.data.ReturnCode === 1) {
        await dispatch("getStageGroupDetailList");
        return response.data;
      } else {
        throw new Error(response.data.Message || "建立階段失敗");
      }
    } catch (err) {
      commit("getDataErr", err);
      console.error('createStageGroup 錯誤詳情:', err);

      // 提供更詳細的錯誤信息
      if (err.response && err.response.status === 500) {
        throw new Error("伺服器內部錯誤，請檢查階段代碼和名稱是否重複，或聯繫系統管理員");
      }

      throw err;
    }
  },

  // 編輯電力卸載階段群組
  async editStageGroup({ commit, dispatch }, data) {
    try {
      commit("getDataBegin");
      
      const formData = new FormData();
      formData.append("SummaryId", data.summaryId || "");
      formData.append("StageId", data.stageId || "");
      formData.append("StageCode", data.stageCode?.toString() || "0");
      formData.append("StageName", data.stageName || "");
      formData.append("UnloadULmt", data.unloadULmt?.toString() || "0");
      formData.append("LoadLLmt", data.loadLLmt?.toString() || "0");
      formData.append("UnloadAlarmTagId", data.unloadAlarmTagId || "");
      formData.append("LoadAlarmTagId", data.loadAlarmTagId || "");
      formData.append("ModifyMode", 3); // 3 = 修改 (數字類型)
      
      const response = await DataService.post("/api/ElectricPowerUnload/EditElectricPowerUnloadStageGroup", formData);
      
      if (response.data.ReturnCode === 1) {
        await dispatch("getStageGroupDetailList");
        return response.data;
      } else {
        throw new Error(response.data.Message || "更新階段失敗");
      }
    } catch (err) {
      commit("getDataErr", err);
      throw err;
    }
  },

  // 編輯電力卸載測點
  async editUnloadTag({ commit, dispatch }, data) {
    try {
      commit("getDataBegin");
      
      const requestData = {
        SummaryId: data.summaryId,
        StageId: data.stageId,
        Detail: data.tagId ? [
          {
            TagId: data.tagId,
            UnloadValue: data.unloadValue?.toString() || "0",
            LoadValue: data.loadValue?.toString() || "0",
            IntervalSecondsForLoad: data.loadIntervalSecond || 0
          }
        ] : []
      };
      
      const response = await DataService.post("/api/ElectricPowerUnload/EditElectricPowerUnloadTag", requestData, {
        "Content-Type": "application/json"
      });
      
      if (response.data.ReturnCode === 1) {
        await dispatch("getStageGroupDetailList");
        return response.data;
      } else {
        throw new Error(response.data.Message || "更新測點失敗");
      }
    } catch (err) {
      commit("getDataErr", err);
      throw err;
    }
  },

  // 編輯電力可消耗測點
  async editConsumableTag({ commit, dispatch }, data) {
    try {
      commit("getDataBegin");
      
      const formData = new FormData();
      formData.append("SummaryId", data.summaryId);
      
      // 添加所有測點 ID
      if (data.consumableTagIdList && data.consumableTagIdList.length > 0) {
        data.consumableTagIdList.forEach(tagId => {
          if (tagId) {
            formData.append("ElectricPowerConsumableTagIdList", tagId);
          }
        });
      }
      
      const response = await DataService.post("/api/ElectricPowerUnload/EditElectricPowerConsumableTagForUnload", formData);
      
      if (response.data.ReturnCode === 1) {
        await dispatch("getStageGroupDetailList");
        return response.data;
      } else {
        throw new Error(response.data.Message || "更新可消耗測點失敗");
      }
    } catch (err) {
      commit("getDataErr", err);
      throw err;
    }
  },

  // 重設電力卸載限制
  async resetUnloadLimit({ commit, dispatch }) {
    try {
      commit("getDataBegin");
      
      // 從當前狀態中獲取階段列表
      const state = this.state.electricPowerUnload;
      const stageList = state.summaryDetailList.flatMap(summary => 
        summary.StageDetailList || []
      ).map(stage => ({
        StageId: stage.StageId,
        UnloadUmt: stage.UnloadULmt || 0,
        StageCode: stage.StageCode
      }));
      
      const requestData = {
        StageValueListJson: JSON.stringify(stageList)
      };
      
      const response = await DataService.post("/api/ElectricPowerUnload/ResetElectricityUnloadLmt", requestData, {
        "Content-Type": "application/json"
      });
      
      if (response.data.ReturnCode === 1) {
        await dispatch("getStageGroupDetailList");
        return response.data;
      } else {
        throw new Error(response.data.Message || "重設失敗");
      }
    } catch (err) {
      commit("getDataErr", err);
      throw err;
    }
  },

  // 更新系統設定
  updateConfig({ commit }, config) {
    commit("updateConfig", config);
  }
};

export default {
  namespaced: true,
  state,
  actions,
  mutations
}; 