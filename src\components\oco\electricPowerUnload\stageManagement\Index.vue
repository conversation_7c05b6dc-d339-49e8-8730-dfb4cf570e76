<template>
  <div>
    <!-- 階段列表 -->
    <sdCards title="階段列表">
      <template #button>
        <a-space>
          <a-button type="primary" @click="openCreateStageModal">
            <unicon name="plus" width="14"></unicon>
            建立階段
          </a-button>
          <a-button @click="handleResetLimit">
            <unicon name="refresh" width="14"></unicon>
            重設卸載限制
          </a-button>
        </a-space>
      </template>

      <a-spin :spinning="loading">
        <!-- 當沒有階段時顯示提示 -->
        <div v-if="stageList.length === 0" class="no-stages">
          <a-empty description="尚未建立任何階段，請點擊上方「建立階段」按鈕來新增階段" />
        </div>
        
        <!-- 階段列表 -->
        <a-collapse v-else v-model="activeKeys" accordion>
          <a-collapse-panel 
            v-for="stage in stageList" 
            :key="stage.StageId"
            :header="`${stage.StageName} (${stage.StageCode})`"
          >
            <template #extra>
              <a-tag :color="stage.UnloadULmt && stage.LoadLLmt ? 'green' : 'default'">
                {{ stage.UnloadULmt && stage.LoadLLmt ? '已設定' : '未設定' }}
              </a-tag>
            </template>

            <!-- 階段基本設定 -->
            <div class="stage-settings">
              <h4>階段設定</h4>
              <a-form :model="stage" layout="inline">
                <a-col :span="8">
                  <a-form-item label="階段名稱">
                    <a-input 
                      v-model="stage.StageName" 
                      style="width: 200px"
                      @blur="updateStage(stage)"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="階段代碼">
                    <a-input-number 
                      v-model="stage.StageCode" 
                      style="width: 100px"
                      :min="1"
                      @blur="updateStage(stage)"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="卸載上限">
                    <a-input-number 
                      v-model="stage.UnloadULmt" 
                      style="width: 120px"
                      :min="0"
                      :step="0.1"
                      placeholder="KW"
                      @blur="updateStage(stage)"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="加載下限">
                    <a-input-number 
                      v-model="stage.LoadLLmt" 
                      style="width: 120px"
                      :min="0"
                      :step="0.1"
                      placeholder="KW"
                      @blur="updateStage(stage)"
                    />
                  </a-form-item>
                </a-col>
              </a-form>

              <!-- 警報測點設定 -->
              <h4>警報測點</h4>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="加載警報測點">
                    <a-select
                      :value="stage.LoadAlarmTag ? stage.LoadAlarmTag.TagId : undefined"
                      style="width: 100%"
                      placeholder="請選擇加載警報測點"
                      allowClear
                      @change="(value) => updateStageAlarmTag(stage, 'load', value)"
                    >
                      <a-select-option 
                        v-for="tag in alarmTagList" 
                        :key="tag.TagId" 
                        :value="tag.TagId"
                      >
                        {{ tag.Description }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="卸載警報測點">
                    <a-select
                      :value="stage.UnloadAlarmTag ? stage.UnloadAlarmTag.TagId : undefined"
                      style="width: 100%"
                      placeholder="請選擇卸載警報測點"
                      allowClear
                      @change="(value) => updateStageAlarmTag(stage, 'unload', value)"
                    >
                      <a-select-option 
                        v-for="tag in alarmTagList" 
                        :key="tag.TagId" 
                        :value="tag.TagId"
                      >
                        {{ tag.Description }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <!-- 階段加/卸載測點 -->
              <h4>階段加/卸載測點</h4>
              <a-table
                :data-source="stage.TagList || []"
                :columns="tagColumns"
                :pagination="false"
                size="small"
                bordered
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'loadValue'">
                    <a-input-number
                      v-model="record.LoadValue"
                      style="width: 100px"
                      :min="0"
                      :step="0.1"
                      @blur="updateTag(record)"
                    />
                  </template>
                  <template v-if="column.key === 'unloadValue'">
                    <a-input-number
                      v-model="record.UnloadValue"
                      style="width: 100px"
                      :min="0"
                      :step="0.1"
                      @blur="updateTag(record)"
                    />
                  </template>
                  <template v-if="column.key === 'intervalSeconds'">
                    <a-input-number
                      v-model="record.IntervalSecondsForLoad"
                      style="width: 80px"
                      :min="0"
                      @blur="updateTag(record)"
                    />
                  </template>
                </template>
              </a-table>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </a-spin>
    </sdCards>

    <!-- 建立階段 Modal -->
    <a-modal
      v-model:visible="createStageModalVisible"
      title="建立新階段"
      :confirm-loading="createStageLoading"
      @ok="handleCreateStage"
      @cancel="closeCreateStageModal"
    >
      <a-form :model="createStageForm" :rules="createStageRules" ref="createStageFormRef">
        <a-form-item label="階段代碼" name="stageCode">
          <a-input-number
            v-model:value="createStageForm.stageCode"
            style="width: 100%"
            :min="1"
            placeholder="請輸入階段代碼"
          />
        </a-form-item>
        <a-form-item label="階段名稱" name="stageName">
          <a-input
            v-model:value="createStageForm.stageName"
            placeholder="請輸入階段名稱"
          />
        </a-form-item>
        <a-form-item label="卸載上限 (KW)" name="unloadULmt">
          <a-input-number
            v-model:value="createStageForm.unloadULmt"
            style="width: 100%"
            :min="0"
            :step="0.1"
            placeholder="請輸入卸載上限"
          />
        </a-form-item>
        <a-form-item label="加載下限 (KW)" name="loadLLmt">
          <a-input-number
            v-model:value="createStageForm.loadLLmt"
            style="width: 100%"
            :min="0"
            :step="0.1"
            placeholder="請輸入加載下限"
          />
        </a-form-item>
        <a-form-item label="加載警報測點" name="loadAlarmTagId">
          <a-select
            v-model:value="createStageForm.loadAlarmTagId"
            placeholder="請選擇加載警報測點"
            allowClear
          >
            <a-select-option 
              v-for="tag in alarmTagList" 
              :key="tag.Id" 
              :value="tag.Id"
            >
              {{ tag.Description || tag.Name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="卸載警報測點" name="unloadAlarmTagId">
          <a-select
            v-model:value="createStageForm.unloadAlarmTagId"
            placeholder="請選擇卸載警報測點"
            allowClear
          >
            <a-select-option 
              v-for="tag in alarmTagList" 
              :key="tag.Id" 
              :value="tag.Id"
            >
              {{ tag.Description || tag.Name }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { Modal } from 'ant-design-vue';

export default {
  name: 'StageManagement',
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    summaryId: {
      type: [String, Number],
      required: true
    },
    stageList: {
      type: Array,
      default: () => []
    },
    tagList: {
      type: Array,
      default: () => []
    },
    alarmTagList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeKeys: [],
      createStageModalVisible: false,
      createStageLoading: false,
      createStageForm: {
        stageCode: undefined,
        stageName: undefined,
        unloadULmt: undefined,
        loadLLmt: undefined,
        loadAlarmTagId: undefined,
        unloadAlarmTagId: undefined
      },
      createStageRules: {
        stageCode: [
          { required: true, message: '請輸入階段代碼', trigger: 'blur' },
          { type: 'number', min: 1, message: '階段代碼必須大於0', trigger: 'blur' }
        ],
        stageName: [{ required: true, message: '請輸入階段名稱', trigger: 'blur' }],
        unloadULmt: [
          { required: true, message: '請輸入卸載上限', trigger: 'blur' },
          { type: 'number', min: 0, message: '卸載上限必須大於0', trigger: 'blur' }
        ],
        loadLLmt: [
          { required: true, message: '請輸入加載下限', trigger: 'blur' },
          { type: 'number', min: 0, message: '加載下限必須大於0', trigger: 'blur' },
          {
            validator: (rule, value) => {
              if (value && this.createStageForm.unloadULmt && value >= this.createStageForm.unloadULmt) {
                return Promise.reject(new Error('加載下限必須小於卸載上限'));
              } else {
                return Promise.resolve();
              }
            },
            trigger: 'blur'
          }
        ]
      }
    };
  },
  computed: {
    // 測點表格欄位定義
    tagColumns() {
      return [
        {
          title: '測點名稱',
          dataIndex: 'Description',
          key: 'description',
          width: 200
        },
        {
          title: '測點說明',
          dataIndex: 'FullTagName',
          key: 'fullTagName',
          width: 300
        },
        {
          title: '加載值',
          dataIndex: 'LoadValue',
          key: 'loadValue',
          width: 120
        },
        {
          title: '卸載值',
          dataIndex: 'UnloadValue',
          key: 'unloadValue',
          width: 120
        },
        {
          title: '加載秒數',
          dataIndex: 'IntervalSecondsForLoad',
          key: 'intervalSeconds',
          width: 100
        }
      ];
    }
  },
  methods: {
    // 更新階段設定
    async updateStage(stage) {
      try {
        await this.$store.dispatch('electricPowerUnload/editStageGroup', {
          summaryId: this.summaryId,
          stageId: stage.StageId,
          stageCode: stage.StageCode,
          stageName: stage.StageName,
          unloadULmt: stage.UnloadULmt,
          loadLLmt: stage.LoadLLmt,
          unloadAlarmTagId: stage.UnloadAlarmTag?.TagId,
          loadAlarmTagId: stage.LoadAlarmTag?.TagId
        });
        
        Modal.success({
          title: '更新成功',
          content: '階段設定已更新'
        });
      } catch (error) {
        Modal.error({
          title: '更新失敗',
          content: error.message || '無法更新階段設定'
        });
      }
    },

    // 更新測點設定
    async updateTag(tag) {
      try {
        await this.$store.dispatch('electricPowerUnload/editUnloadTag', {
          summaryId: this.summaryId,
          stageId: this.stageList.find(s => s.TagList?.includes(tag))?.StageId,
          tagId: tag.TagId,
          loadValue: tag.LoadValue,
          unloadValue: tag.UnloadValue,
          loadIntervalSecond: tag.IntervalSecondsForLoad
        });
        
        Modal.success({
          title: '更新成功',
          content: '測點設定已更新'
        });
      } catch (error) {
        Modal.error({
          title: '更新失敗',
          content: error.message || '無法更新測點設定'
        });
      }
    },

    // 更新階段警報測點
    async updateStageAlarmTag(stage, type, tagId) {
      try {
        await this.$store.dispatch('electricPowerUnload/editStageGroup', {
          summaryId: this.summaryId,
          stageId: stage.StageId,
          stageCode: stage.StageCode,
          stageName: stage.StageName,
          unloadULmt: stage.UnloadULmt,
          loadLLmt: stage.LoadLLmt,
          unloadAlarmTagId: type === 'unload' ? tagId : stage.UnloadAlarmTag?.TagId,
          loadAlarmTagId: type === 'load' ? tagId : stage.LoadAlarmTag?.TagId
        });
        
        Modal.success({
          title: '更新成功',
          content: `${type}警報測點已更新`
        });
      } catch (error) {
        Modal.error({
          title: '更新失敗',
          content: error.message || `無法更新${type}警報測點`
        });
      }
    },

    // 重設卸載限制
    async handleResetLimit() {
      try {
        await this.$store.dispatch('electricPowerUnload/resetUnloadLimit');
        Modal.success({
          title: '重設成功',
          content: '卸載限制已重設'
        });
      } catch (error) {
        Modal.error({
          title: '重設失敗',
          content: error.message || '無法重設卸載限制'
        });
      }
    },

    // 開啟建立階段 Modal
    openCreateStageModal() {
      this.createStageModalVisible = true;
      this.$nextTick(() => {
        this.$refs.createStageFormRef.resetFields();
      });
    },

    // 關閉建立階段 Modal
    closeCreateStageModal() {
      this.createStageModalVisible = false;
    },

    // 處理建立階段
    async handleCreateStage() {
      try {
        await this.$refs.createStageFormRef.validateFields();
        this.createStageLoading = true;
        await this.$store.dispatch('electricPowerUnload/createStageGroup', {
          summaryId: this.summaryId,
          stageCode: this.createStageForm.stageCode,
          stageName: this.createStageForm.stageName,
          unloadULmt: this.createStageForm.unloadULmt,
          loadLLmt: this.createStageForm.loadLLmt,
          loadAlarmTagId: this.createStageForm.loadAlarmTagId,
          unloadAlarmTagId: this.createStageForm.unloadAlarmTagId
        });
        this.closeCreateStageModal();
        Modal.success({
          title: '建立成功',
          content: '新階段已建立'
        });
        this.$emit('stageListUpdated'); // 通知父組件階段列表已更新
      } catch (error) {
        Modal.error({
          title: '建立失敗',
          content: error.message || '無法建立新階段'
        });
      } finally {
        this.createStageLoading = false;
      }
    }
  }
};
</script>

<style scoped>
.stage-settings {
  padding: 16px;
}

.stage-settings h4 {
  margin-bottom: 16px;
  color: #1890ff;
  font-weight: 600;
}

.ant-form-item {
  margin-bottom: 16px;
}

.no-stages {
  padding: 20px;
  text-align: center;
}
</style> 